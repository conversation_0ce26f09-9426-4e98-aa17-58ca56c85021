1、
这是smolagents的官方文档的链接: https://huggingface.co/docs/smolagents/index 请使用smolagents框架为我开发一款能够获取最新AI资讯并且将AI资讯改写为微信公众号的AI智能体。
在执行开发之前，请为我生成PRD文档，在我确认之后，再将PRD文档拆解为开发任务。
当我确认任务步骤无误之后，执行全自动开发。

2、拆解成具体的任务步骤
3、确认无误，agent中的LLM使用gpt-4o模型，这是OpenAI的API key：sk-proj-PNbV94VbOGdwryru6Qzy7zUo0iJCSZHRa6hGgAUnCdSjDofPj i5_XH_vpXPu3GGyJ9klw6y_HNT3BlbkFJpEZMu1g7c4CtkloYRYzD1




userguideline:
编写Python代码时，请严格按照以下规范执行：

核心规范（必须遵循）：
- 4空格缩进，禁用Tab
- 行长度≤88字符
- snake_case命名变量/函数，PascalCase命名类
- 优先使用f-string格式化

建议规范：
- 多行集合末尾加逗号
- 常量全大写命名
- 遵循PEP 8导入顺序

请在代码中体现这些规范，如有冲突以核心规范为准。


