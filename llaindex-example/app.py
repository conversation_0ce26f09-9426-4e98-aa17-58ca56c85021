from __future__ import annotations

import hashlib
import json
import os
from io import Bytes<PERSON>
from pathlib import Path
from typing import Iterable, List, Tuple

import streamlit as st
from pypdf import PdfReader

from llama_index.core import (
    Document,
    Settings,
    StorageContext,
    VectorStoreIndex,
    load_index_from_storage,
)
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.vector_stores import SimpleVectorStore
from llama_index.embeddings.huggingface import HuggingFaceEmbedding


# ----------------------------
# App constants & paths
# ----------------------------
APP_TITLE = "本地知识库（LlamaIndex + Streamlit）"
STORAGE_DIR = Path("storage")
UPLOAD_DIR = Path("data") / "uploads"
CONFIG_PATH = STORAGE_DIR / "config.json"
DOC_REGISTRY_PATH = STORAGE_DIR / "doc_registry.json"

DEFAULT_MODEL = "BAAI/bge-small-zh-v1.5"
MODEL_CANDIDATES = [
    "BAAI/bge-small-zh-v1.5",
    "BAAI/bge-m3",
    "sentence-transformers/all-MiniLM-L6-v2",
]

CHUNK_SIZE = 800
CHUNK_OVERLAP = 100


# ----------------------------
# Utilities
# ----------------------------

def ensure_dirs() -> None:
    STORAGE_DIR.mkdir(parents=True, exist_ok=True)
    UPLOAD_DIR.mkdir(parents=True, exist_ok=True)


def sha256_bytes(data: bytes) -> str:
    return hashlib.sha256(data).hexdigest()


def save_stream_to_file(stream, dst: Path) -> None:
    dst.parent.mkdir(parents=True, exist_ok=True)
    with open(dst, "wb") as f:
        f.write(stream.getbuffer())


def load_config() -> dict:
    if CONFIG_PATH.exists():
        try:
            return json.loads(CONFIG_PATH.read_text(encoding="utf-8"))
        except Exception:
            return {}
    return {}


def save_config(cfg: dict) -> None:
    CONFIG_PATH.write_text(json.dumps(cfg, ensure_ascii=False, indent=2),
                           encoding="utf-8")


def load_doc_registry() -> dict:
    if DOC_REGISTRY_PATH.exists():
        try:
            return json.loads(DOC_REGISTRY_PATH.read_text(encoding="utf-8"))
        except Exception:
            return {}
    return {}


def save_doc_registry(reg: dict) -> None:
    DOC_REGISTRY_PATH.write_text(json.dumps(reg, ensure_ascii=False, indent=2),
                                 encoding="utf-8")


def build_embed_model(model_name: str) -> HuggingFaceEmbedding:
    return HuggingFaceEmbedding(
        model_name=model_name,
        # Normalize for cosine similarity
        embed_batch_size=32,
    )


def init_settings(model_name: str) -> None:
    Settings.embed_model = build_embed_model(model_name)
    Settings.node_parser = SentenceSplitter(
        chunk_size=CHUNK_SIZE, chunk_overlap=CHUNK_OVERLAP
    )
    # 不使用 LLM 生成答案，仅返回召回片段
    Settings.llm = None


def try_load_index() -> Tuple[VectorStoreIndex | None, StorageContext | None]:
    if not STORAGE_DIR.exists():
        return None, None
    try:
        vector_store = SimpleVectorStore.from_persist_dir(STORAGE_DIR)
        storage_ctx = StorageContext.from_defaults(
            persist_dir=str(STORAGE_DIR), vector_store=vector_store
        )
        index = load_index_from_storage(storage_ctx)
        return index, storage_ctx
    except Exception:
        return None, None


def create_empty_index() -> Tuple[VectorStoreIndex, StorageContext]:
    vector_store = SimpleVectorStore()
    storage_ctx = StorageContext.from_defaults(vector_store=vector_store)
    # 构建一个空索引（不传文档）
    index = VectorStoreIndex.from_documents([], storage_context=storage_ctx)
    storage_ctx.persist(persist_dir=str(STORAGE_DIR))
    return index, storage_ctx


def get_or_init_index(model_name: str) -> Tuple[VectorStoreIndex, StorageContext]:
    cfg = load_config()
    if cfg.get("embed_model") and cfg["embed_model"] != model_name:
        st.warning(
            "检测到历史索引用的嵌入模型与当前选择不同，建议先清空索引后重建。"
        )
    init_settings(model_name)

    index, storage_ctx = try_load_index()
    if index is not None and storage_ctx is not None:
        return index, storage_ctx

    index, storage_ctx = create_empty_index()
    save_config({"embed_model": model_name})
    return index, storage_ctx


# ----------------------------
# Document reading
# ----------------------------

def read_txt(file_bytes: bytes, file_name: str) -> List[Document]:
    try:
        text = file_bytes.decode("utf-8")
    except UnicodeDecodeError:
        text = file_bytes.decode("utf-8", errors="ignore")
    meta = {"file_name": file_name, "file_type": "txt"}
    return [Document(text=text, metadata=meta)]


def read_md(file_bytes: bytes, file_name: str) -> List[Document]:
    # Markdown 作为纯文本索引；渲染在前端处理
    try:
        text = file_bytes.decode("utf-8")
    except UnicodeDecodeError:
        text = file_bytes.decode("utf-8", errors="ignore")
    meta = {"file_name": file_name, "file_type": "md"}
    return [Document(text=text, metadata=meta)]


def read_pdf(file_bytes: bytes, file_name: str) -> List[Document]:
    results: List[Document] = []
    reader = PdfReader(BytesIO(file_bytes))
    for i, page in enumerate(reader.pages, start=1):
        try:
            text = page.extract_text() or ""
        except Exception:
            text = ""
        meta = {
            "file_name": file_name,
            "file_type": "pdf",
            "page_number": i,
        }
        if text.strip():
            results.append(Document(text=text, metadata=meta))
    return results


def build_documents(
    files: Iterable[Tuple[str, bytes]],
) -> Tuple[List[Document], List[str]]:
    docs: List[Document] = []
    skipped: List[str] = []
    for file_name, content in files:
        suffix = Path(file_name).suffix.lower()
        if suffix == ".txt":
            docs.extend(read_txt(content, file_name))
        elif suffix in {".md", ".markdown"}:
            docs.extend(read_md(content, file_name))
        elif suffix == ".pdf":
            try:
                docs.extend(read_pdf(content, file_name))
            except Exception:
                skipped.append(file_name)
        else:
            skipped.append(file_name)
    return docs, skipped


def upsert_documents(
    index: VectorStoreIndex, storage_ctx: StorageContext, docs: List[Document]
) -> int:
    count = 0
    for doc in docs:
        index.insert(doc)
        count += 1
    storage_ctx.persist(persist_dir=str(STORAGE_DIR))
    return count


# ----------------------------
# Streamlit UI
# ----------------------------

def sidebar_controls() -> Tuple[str, int, bool]:
    st.sidebar.header("设置")
    model_name = st.sidebar.selectbox(
        "嵌入模型（离线）",
        MODEL_CANDIDATES,
        index=MODEL_CANDIDATES.index(DEFAULT_MODEL),
    )
    top_k = int(st.sidebar.slider("Top-K 检索数量", 1, 20, 5))

    clear = st.sidebar.button("清空索引与缓存", type="secondary")
    return model_name, top_k, clear


def clear_all_storage() -> None:
    if STORAGE_DIR.exists():
        for root, dirs, files in os.walk(STORAGE_DIR, topdown=False):
            for name in files:
                Path(root, name).unlink(missing_ok=True)
            for name in dirs:
                Path(root, name).rmdir()
        STORAGE_DIR.rmdir()


def handle_uploads(index: VectorStoreIndex, storage_ctx: StorageContext) -> None:
    st.subheader("上传文档（支持 txt / md / pdf）")
    uploaded = st.file_uploader(
        "选择文件：",
        type=["txt", "md", "markdown", "pdf"],
        accept_multiple_files=True,
    )

    if not uploaded:
        return

    files: List[Tuple[str, bytes]] = []
    doc_reg = load_doc_registry()

    for f in uploaded:
        data = f.read()
        file_hash = sha256_bytes(data)
        # 去重：同内容文件跳过
        if file_hash in doc_reg:
            st.info(f"跳过重复：{f.name}")
            continue
        files.append((f.name, data))
        # 保存原文件到本地（可选）
        save_stream_to_file(BytesIO(data), UPLOAD_DIR / f.name)
        doc_reg[file_hash] = {"file_name": f.name}

    if not files:
        return

    with st.spinner("解析与索引中…"):
        docs, skipped = build_documents(files)
        added = upsert_documents(index, storage_ctx, docs)
        save_doc_registry(doc_reg)

    st.success(f"新增 {added} 个文档块。")
    if skipped:
        st.warning("未处理的文件类型/失败：" + ", ".join(skipped))


def handle_query(index: VectorStoreIndex, top_k: int) -> None:
    st.subheader("检索")
    query = st.text_input("输入自然语言问题：", value="", max_chars=300)
    run = st.button("检索")

    if not run or not query.strip():
        return

    query_engine = index.as_query_engine(
        similarity_top_k=top_k, response_mode="no_text"
    )
    with st.spinner("检索中…"):
        response = query_engine.query(query)

    if not getattr(response, "source_nodes", None):
        st.info("未检索到结果。")
        return

    st.write("检索结果（按相似度排序）：")
    for i, sn in enumerate(response.source_nodes, start=1):
        node = sn.node
        meta = dict(node.metadata or {})
        file_name = meta.get("file_name", "未知文件")
        page = meta.get("page_number")
        node_id = getattr(node, "node_id", "-")
        score = getattr(sn, "score", None)

        title = f"#{i} {file_name}"
        if page is not None:
            title += f" · 第 {page} 页"
        if score is not None:
            title += f" · 相似度 {score:.4f}"
        title += f" · 节点 {node_id[:8]}"

        with st.expander(title, expanded=i <= 3):
            st.text(node.get_content()[:1500])


def main() -> None:
    st.set_page_config(page_title=APP_TITLE, layout="wide")
    st.title(APP_TITLE)

    ensure_dirs()
    model_name, top_k, clear = sidebar_controls()

    if clear:
        clear_all_storage()
        st.experimental_rerun()

    index, storage_ctx = get_or_init_index(model_name)

    with st.container():
        handle_uploads(index, storage_ctx)

    st.divider()

    with st.container():
        handle_query(index, top_k)


if __name__ == "__main__":
    main()

