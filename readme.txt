
userguideline:
编写Python代码时，请严格按照以下规范执行：

核心规范（必须遵循）：
- 4空格缩进，禁用Tab
- 行长度≤88字符
- snake_case命名变量/函数，PascalCase命名类
- 优先使用f-string格式化

建议规范：
- 多行集合末尾加逗号
- 常量全大写命名
- 遵循PEP 8导入顺序

请在代码中体现这些规范，如有冲突以核心规范为准。

# 快速开始
cd ai_news_agent
python deploy.py

python -m ai_news_agent.cli run --count 5
export OPENAI_API_KEY="********************************************************************************************************************************************************************"



$env:OPENAI_API_KEY = "********************************************************************************************************************************************************************"
set OPENAI_API_KEY=********************************************************************************************************************************************************************
echo $env:OPENAI_API_KEY
echo %OPENAI_API_KEY%
