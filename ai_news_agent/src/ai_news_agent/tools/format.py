"""
格式化工具 - 输出Markdown格式和微信公众号标准格式
"""

import json
import re
from datetime import datetime
from typing import Dict, Any, List, Optional
from smolagents import Tool

from ..utils.logger import get_logger

logger = get_logger(__name__)


class FormatTool(Tool):
    """
    文章格式化工具，输出Markdown格式和微信公众号标准格式
    
    功能：
    - Markdown格式输出
    - 微信公众号格式输出
    - 图片占位符生成
    - SEO优化标签
    - 发布时间建议
    """
    
    name = "format_article"
    description = """
    将文章内容格式化为指定格式，支持Markdown和微信公众号格式。
    
    参数:
    - title (str): 文章标题
    - content (str): 文章内容
    - format_type (str, 可选): 格式类型，支持 'markdown', 'wechat', 'both'，默认'both'
    - add_images (bool, 可选): 是否添加图片占位符，默认True
    - add_seo (bool, 可选): 是否添加SEO标签，默认True
    - author (str, 可选): 作者名称
    - tags (list, 可选): 标签列表
    
    返回: 包含格式化结果的JSON字符串
    """
    
    inputs = {
        "title": {
            "type": "string",
            "description": "文章标题"
        },
        "content": {
            "type": "string",
            "description": "文章内容"
        },
        "format_type": {
            "type": "string",
            "description": "格式类型：markdown, wechat, both",
            "default": "both",
            "nullable": True
        },
        "add_images": {
            "type": "boolean",
            "description": "是否添加图片占位符",
            "default": True,
            "nullable": True
        },
        "add_seo": {
            "type": "boolean",
            "description": "是否添加SEO标签",
            "default": True,
            "nullable": True
        },
        "author": {
            "type": "string",
            "description": "作者名称",
            "default": "AI News Agent",
            "nullable": True
        },
        "tags": {
            "type": "array",
            "description": "标签列表",
            "default": [],
            "nullable": True
        }
    }
    
    output_type = "string"
    
    def __init__(self):
        super().__init__()
        
        # 图片建议模板
        self.image_suggestions = {
            "intro": "文章开头配图：可以是相关技术的概念图或公司logo",
            "content": "正文配图：技术示意图、产品截图或相关场景图",
            "conclusion": "结尾配图：总结性图表或未来展望图"
        }
        
        # 微信公众号样式模板
        self.wechat_styles = {
            "title": "font-size: 18px; font-weight: bold; color: #333; margin-bottom: 10px;",
            "subtitle": "font-size: 16px; font-weight: bold; color: #666; margin: 15px 0 8px 0;",
            "paragraph": "font-size: 14px; line-height: 1.6; color: #333; margin-bottom: 12px;",
            "highlight": "background-color: #fff3cd; padding: 8px 12px; border-left: 4px solid #ffc107; margin: 10px 0;",
            "quote": "font-style: italic; color: #666; border-left: 3px solid #ddd; padding-left: 15px; margin: 15px 0;"
        }
    
    def forward(
        self,
        title: str,
        content: str,
        format_type: str = "both",
        add_images: bool = True,
        add_seo: bool = True,
        author: str = "AI News Agent",
        tags: List[str] = None
    ) -> str:
        """格式化文章"""
        try:
            logger.info(f"开始格式化文章: {title}, 格式: {format_type}")
            
            if tags is None:
                tags = []
            
            result = {
                "title": title,
                "author": author,
                "publish_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "tags": tags,
                "success": True
            }
            
            # 生成Markdown格式
            if format_type in ["markdown", "both"]:
                result["markdown"] = self._format_markdown(
                    title, content, author, tags, add_images, add_seo
                )
            
            # 生成微信公众号格式
            if format_type in ["wechat", "both"]:
                result["wechat"] = self._format_wechat(
                    title, content, author, add_images
                )
            
            # 生成SEO信息
            if add_seo:
                result["seo"] = self._generate_seo_info(title, content, tags)
            
            # 生成图片建议
            if add_images:
                result["image_suggestions"] = self._generate_image_suggestions(content)
            
            # 生成发布建议
            result["publish_suggestions"] = self._generate_publish_suggestions(content, tags)
            
            # 统计信息
            result["statistics"] = self._calculate_statistics(title, content)
            
            logger.info("文章格式化完成")
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"文章格式化失败: {str(e)}")
            return json.dumps({
                "error": f"文章格式化失败: {str(e)}",
                "success": False
            }, ensure_ascii=False)
    
    def _format_markdown(
        self, 
        title: str, 
        content: str, 
        author: str, 
        tags: List[str],
        add_images: bool,
        add_seo: bool
    ) -> str:
        """格式化为Markdown"""
        
        markdown_parts = []
        
        # 文档头部信息
        if add_seo:
            markdown_parts.append("---")
            markdown_parts.append(f"title: \"{title}\"")
            markdown_parts.append(f"author: \"{author}\"")
            markdown_parts.append(f"date: \"{datetime.now().strftime('%Y-%m-%d')}\"")
            if tags:
                markdown_parts.append(f"tags: [{', '.join([f'\"{tag}\"' for tag in tags])}]")
            markdown_parts.append(f"description: \"{self._generate_description(content)}\"")
            markdown_parts.append("---")
            markdown_parts.append("")
        
        # 标题
        markdown_parts.append(f"# {title}")
        markdown_parts.append("")
        
        # 作者和日期信息
        markdown_parts.append(f"**作者**: {author}")
        markdown_parts.append(f"**发布时间**: {datetime.now().strftime('%Y年%m月%d日')}")
        markdown_parts.append("")
        
        # 添加开头图片占位符
        if add_images:
            markdown_parts.append("![文章配图](image-placeholder-1.jpg)")
            markdown_parts.append("*图片说明：文章主题相关配图*")
            markdown_parts.append("")
        
        # 处理内容
        formatted_content = self._process_content_for_markdown(content, add_images)
        markdown_parts.append(formatted_content)
        
        # 添加标签
        if tags:
            markdown_parts.append("")
            markdown_parts.append("## 标签")
            markdown_parts.append("")
            tag_links = [f"[{tag}](#{tag.replace(' ', '-').lower()})" for tag in tags]
            markdown_parts.append(" | ".join(tag_links))
        
        # 添加版权信息
        markdown_parts.append("")
        markdown_parts.append("---")
        markdown_parts.append("")
        markdown_parts.append("*本文由AI News Agent自动生成，如有转载请注明出处。*")
        markdown_parts.append("")
        markdown_parts.append(f"*生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        
        return "\n".join(markdown_parts)
    
    def _format_wechat(self, title: str, content: str, author: str, add_images: bool) -> str:
        """格式化为微信公众号格式"""
        
        wechat_parts = []
        
        # 标题（微信公众号样式）
        wechat_parts.append(f'<h1 style="{self.wechat_styles["title"]}">{title}</h1>')
        wechat_parts.append("")
        
        # 作者信息
        wechat_parts.append(f'<p style="font-size: 12px; color: #999; text-align: right;">作者：{author} | {datetime.now().strftime("%Y-%m-%d")}</p>')
        wechat_parts.append("")
        
        # 添加开头图片
        if add_images:
            wechat_parts.append('<div style="text-align: center; margin: 20px 0;">')
            wechat_parts.append('<img src="image-placeholder-1.jpg" style="max-width: 100%; height: auto;" alt="文章配图">')
            wechat_parts.append('<p style="font-size: 12px; color: #999; text-align: center;">图片说明：文章主题相关配图</p>')
            wechat_parts.append('</div>')
            wechat_parts.append("")
        
        # 处理内容
        formatted_content = self._process_content_for_wechat(content, add_images)
        wechat_parts.append(formatted_content)
        
        # 添加分割线
        wechat_parts.append("")
        wechat_parts.append('<hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">')
        wechat_parts.append("")
        
        # 添加关注提示
        wechat_parts.append('<div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">')
        wechat_parts.append('<p style="margin: 0; font-size: 14px; color: #666;">📱 关注我们获取更多AI资讯</p>')
        wechat_parts.append('<p style="margin: 5px 0 0 0; font-size: 12px; color: #999;">AI News Agent - 您的AI资讯助手</p>')
        wechat_parts.append('</div>')
        
        return "\n".join(wechat_parts)
    
    def _process_content_for_markdown(self, content: str, add_images: bool) -> str:
        """为Markdown处理内容"""
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        processed_parts = []
        
        for i, paragraph in enumerate(paragraphs):
            # 检测是否为标题
            if self._is_title_paragraph(paragraph):
                processed_parts.append(f"## {paragraph}")
            else:
                processed_parts.append(paragraph)
            
            # 在适当位置添加图片
            if add_images and i == len(paragraphs) // 2:
                processed_parts.append("")
                processed_parts.append("![内容配图](image-placeholder-2.jpg)")
                processed_parts.append("*图片说明：相关技术示意图*")
                processed_parts.append("")
        
        return "\n\n".join(processed_parts)
    
    def _process_content_for_wechat(self, content: str, add_images: bool) -> str:
        """为微信公众号处理内容"""
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        processed_parts = []
        
        for i, paragraph in enumerate(paragraphs):
            # 检测特殊格式
            if paragraph.startswith('🔥') or paragraph.startswith('💡') or paragraph.startswith('🚀'):
                # 高亮段落
                processed_parts.append(f'<div style="{self.wechat_styles["highlight"]}">{paragraph}</div>')
            elif self._is_title_paragraph(paragraph):
                # 子标题
                processed_parts.append(f'<h2 style="{self.wechat_styles["subtitle"]}">{paragraph}</h2>')
            elif paragraph.startswith('🤔') or paragraph.startswith('💭'):
                # 问题段落
                processed_parts.append(f'<div style="{self.wechat_styles["quote"]}">{paragraph}</div>')
            else:
                # 普通段落
                processed_parts.append(f'<p style="{self.wechat_styles["paragraph"]}">{paragraph}</p>')
            
            # 在中间位置添加图片
            if add_images and i == len(paragraphs) // 2:
                processed_parts.append('<div style="text-align: center; margin: 20px 0;">')
                processed_parts.append('<img src="image-placeholder-2.jpg" style="max-width: 100%; height: auto;" alt="内容配图">')
                processed_parts.append('<p style="font-size: 12px; color: #999; text-align: center;">图片说明：相关技术示意图</p>')
                processed_parts.append('</div>')
        
        return "\n".join(processed_parts)
    
    def _is_title_paragraph(self, paragraph: str) -> bool:
        """判断是否为标题段落"""
        # 简单的标题检测逻辑
        if len(paragraph) < 50 and ('：' in paragraph or '?' in paragraph or '？' in paragraph):
            return True
        if paragraph.startswith(('一、', '二、', '三、', '四、', '五、')):
            return True
        if paragraph.startswith(('1.', '2.', '3.', '4.', '5.')):
            return True
        return False
    
    def _generate_seo_info(self, title: str, content: str, tags: List[str]) -> Dict[str, Any]:
        """生成SEO信息"""
        # 提取关键词
        keywords = self._extract_seo_keywords(title, content, tags)
        
        # 生成描述
        description = self._generate_description(content)
        
        return {
            "title": title,
            "description": description,
            "keywords": keywords,
            "og_title": title,
            "og_description": description,
            "og_type": "article",
            "twitter_card": "summary_large_image",
            "twitter_title": title,
            "twitter_description": description
        }
    
    def _extract_seo_keywords(self, title: str, content: str, tags: List[str]) -> List[str]:
        """提取SEO关键词"""
        keywords = set(tags)
        
        # 从标题提取
        title_words = re.findall(r'\b\w+\b', title.lower())
        keywords.update([word for word in title_words if len(word) > 2])
        
        # AI相关关键词
        ai_keywords = [
            'ai', '人工智能', 'gpt', 'chatgpt', '机器学习', '深度学习',
            'openai', 'google', 'microsoft', '大模型', 'llm'
        ]
        
        for keyword in ai_keywords:
            if keyword.lower() in (title + content).lower():
                keywords.add(keyword)
        
        return list(keywords)[:10]  # 限制关键词数量
    
    def _generate_description(self, content: str) -> str:
        """生成文章描述"""
        # 取前150个字符作为描述
        clean_content = re.sub(r'[🔥💡🚀📢⚡🎯🌟🔍💎🎉🤔💭📝👍🔄]', '', content)
        sentences = clean_content.split('。')[:2]
        description = '。'.join(sentences) + '。'
        
        if len(description) > 150:
            description = description[:147] + '...'
        
        return description
    
    def _generate_image_suggestions(self, content: str) -> List[Dict[str, str]]:
        """生成图片建议"""
        suggestions = []
        
        # 开头图片
        suggestions.append({
            "position": "intro",
            "description": "文章开头配图：可以是相关技术的概念图或公司logo",
            "alt_text": "文章主题配图",
            "size_suggestion": "16:9 横图，建议尺寸 900x500px"
        })
        
        # 内容图片
        if len(content) > 1000:
            suggestions.append({
                "position": "content",
                "description": "正文配图：技术示意图、产品截图或相关场景图",
                "alt_text": "技术示意图",
                "size_suggestion": "4:3 或 1:1 方图，建议尺寸 600x450px"
            })
        
        # 结尾图片
        suggestions.append({
            "position": "conclusion",
            "description": "结尾配图：总结性图表或未来展望图",
            "alt_text": "总结配图",
            "size_suggestion": "16:9 横图，建议尺寸 800x450px"
        })
        
        return suggestions
    
    def _generate_publish_suggestions(self, content: str, tags: List[str]) -> Dict[str, Any]:
        """生成发布建议"""
        # 分析内容类型
        content_type = "news"
        if "突破" in content or "发布" in content:
            content_type = "breaking"
        elif "分析" in content or "解读" in content:
            content_type = "analysis"
        elif "教程" in content or "如何" in content:
            content_type = "tutorial"
        
        # 建议发布时间
        current_hour = datetime.now().hour
        if content_type == "breaking":
            best_time = "立即发布（突发新闻）"
        elif 9 <= current_hour <= 11:
            best_time = "上午10:00-11:00（工作日黄金时间）"
        elif 14 <= current_hour <= 16:
            best_time = "下午15:00-16:00（下午茶时间）"
        elif 19 <= current_hour <= 21:
            best_time = "晚上20:00-21:00（晚间黄金时间）"
        else:
            best_time = "建议在上午10:00或晚上20:00发布"
        
        return {
            "content_type": content_type,
            "best_publish_time": best_time,
            "target_audience": "AI技术爱好者、科技从业者",
            "estimated_read_time": f"{len(content.replace(' ', '')) // 300 + 1}分钟",
            "engagement_tips": [
                "在开头提出引人思考的问题",
                "使用表情符号增加趣味性",
                "在结尾鼓励读者评论互动",
                "添加相关话题标签"
            ]
        }
    
    def _calculate_statistics(self, title: str, content: str) -> Dict[str, Any]:
        """计算统计信息"""
        return {
            "title_length": len(title),
            "content_length": len(content.replace(" ", "")),
            "paragraph_count": len([p for p in content.split('\n\n') if p.strip()]),
            "estimated_read_time": f"{len(content.replace(' ', '')) // 300 + 1}分钟",
            "emoji_count": len(re.findall(r'[🔥💡🚀📢⚡🎯🌟🔍💎🎉🤔💭📝👍🔄]', content)),
            "question_count": content.count('？') + content.count('?'),
            "exclamation_count": content.count('！') + content.count('!')
        }
