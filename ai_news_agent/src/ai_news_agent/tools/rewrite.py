"""
内容改写工具 - 基于LLM将技术文章转化为公众号文章
"""

import json
from typing import Dict, Any, Optional, List
from smolagents import Tool

from ..utils.logger import get_logger

logger = get_logger(__name__)


class RewriteTool(Tool):
    """
    内容改写工具，基于LLM将技术性AI资讯改写为适合微信公众号的文章格式
    
    功能：
    - 标题优化：技术性标题转化为吸引人的公众号标题
    - 内容结构化：重新组织为引言、正文、总结的结构
    - 语言风格转换：技术性语言转化为通俗易懂的表达
    - 互动元素添加：添加问题引导、观点讨论等
    """
    
    name = "rewrite_content"
    description = """
    将技术性AI资讯改写为适合微信公众号发布的文章格式。
    
    参数:
    - content (str): 原始文章内容
    - title (str): 原始标题
    - style (str, 可选): 改写风格，支持 'wechat', 'blog', 'news'，默认'wechat'
    - target_length (int, 可选): 目标字数，默认1500
    - add_interactions (bool, 可选): 是否添加互动元素，默认True
    - tone (str, 可选): 语调，支持 'professional', 'casual', 'enthusiastic'，默认'casual'
    
    返回: 包含改写结果的JSON字符串，包含新标题、改写内容、关键点等
    """
    
    inputs = {
        "content": {
            "type": "string",
            "description": "原始文章内容"
        },
        "title": {
            "type": "string",
            "description": "原始标题"
        },
        "style": {
            "type": "string",
            "description": "改写风格：wechat, blog, news",
            "default": "wechat",
            "nullable": True
        },
        "target_length": {
            "type": "integer",
            "description": "目标字数",
            "default": 1500,
            "nullable": True
        },
        "add_interactions": {
            "type": "boolean",
            "description": "是否添加互动元素",
            "default": True,
            "nullable": True
        },
        "tone": {
            "type": "string",
            "description": "语调：professional, casual, enthusiastic",
            "default": "casual",
            "nullable": True
        }
    }
    
    output_type = "string"
    
    def __init__(self):
        super().__init__()
        
        # 微信公众号标题模板
        self.wechat_title_templates = [
            "🔥 {topic}又有新突破！{key_point}",
            "💡 {topic}最新进展：{key_point}",
            "🚀 重磅！{topic}{key_point}",
            "📢 {topic}新动态：{key_point}",
            "⚡ {topic}再次刷新认知！{key_point}",
            "🎯 {topic}的{key_point}，你了解吗？",
            "🌟 {topic}领域传来好消息：{key_point}",
            "🔍 深度解析：{topic}的{key_point}",
            "💎 {topic}新发现：{key_point}",
            "🎉 {topic}迎来重大突破：{key_point}"
        ]
        
        # 互动元素模板
        self.interaction_templates = {
            "question": [
                "你觉得这项技术会如何改变我们的生活？",
                "对于这个发展，你有什么看法？",
                "你认为这项技术的应用前景如何？",
                "这是否会成为行业的转折点？",
                "你最期待这项技术在哪个领域的应用？"
            ],
            "call_to_action": [
                "如果你对AI技术感兴趣，记得关注我们获取更多资讯！",
                "想了解更多AI前沿动态？点击关注不迷路！",
                "觉得有用的话，别忘了点赞和分享哦～",
                "有什么想法欢迎在评论区讨论！",
                "更多AI资讯，我们下期见！"
            ],
            "engagement": [
                "💭 你怎么看？",
                "🤔 这让你想到了什么？",
                "📝 欢迎留言分享你的观点",
                "👍 如果觉得有用请点赞支持",
                "🔄 转发让更多人看到"
            ]
        }
    
    def forward(
        self,
        content: str,
        title: str,
        style: str = "wechat",
        target_length: int = 1500,
        add_interactions: bool = True,
        tone: str = "casual"
    ) -> str:
        """改写内容"""
        try:
            logger.info(f"开始改写内容，风格: {style}, 目标长度: {target_length}")
            
            # 分析原始内容
            analysis = self._analyze_content(content, title)
            
            # 生成新标题
            new_title = self._generate_title(title, analysis, style)
            
            # 改写内容
            rewritten_content = self._rewrite_content(
                content, analysis, style, target_length, tone
            )
            
            # 添加互动元素
            if add_interactions:
                rewritten_content = self._add_interactions(rewritten_content, style)
            
            # 生成摘要和关键点
            summary = self._generate_summary(rewritten_content)
            key_points = self._extract_key_points(rewritten_content)
            
            result = {
                "original_title": title,
                "new_title": new_title,
                "original_content": content,
                "rewritten_content": rewritten_content,
                "summary": summary,
                "key_points": key_points,
                "style": style,
                "tone": tone,
                "word_count": len(rewritten_content.replace(" ", "")),
                "target_length": target_length,
                "has_interactions": add_interactions,
                "success": True
            }
            
            logger.info(f"改写完成，新字数: {result['word_count']}")
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"内容改写失败: {str(e)}")
            return json.dumps({
                "error": f"内容改写失败: {str(e)}",
                "success": False
            }, ensure_ascii=False)
    
    def _analyze_content(self, content: str, title: str) -> Dict[str, Any]:
        """分析原始内容"""
        import re
        
        # 提取关键信息
        analysis = {
            "main_topic": "",
            "key_technologies": [],
            "companies": [],
            "numbers": [],
            "achievements": [],
            "implications": []
        }
        
        # 识别主要话题
        ai_topics = [
            "GPT", "ChatGPT", "LLM", "大语言模型", "人工智能", "AI", "机器学习", 
            "深度学习", "神经网络", "自然语言处理", "计算机视觉", "机器人"
        ]
        
        for topic in ai_topics:
            if topic.lower() in (title + content).lower():
                analysis["main_topic"] = topic
                break
        
        # 识别公司名称
        companies = [
            "OpenAI", "Google", "Microsoft", "Meta", "Apple", "Amazon", "Tesla",
            "Anthropic", "DeepMind", "百度", "阿里巴巴", "腾讯", "字节跳动"
        ]
        
        for company in companies:
            if company in title + content:
                analysis["companies"].append(company)
        
        # 提取数字和成就
        numbers = re.findall(r'\d+(?:\.\d+)?%?', content)
        analysis["numbers"] = numbers[:5]  # 限制数量
        
        # 识别成就关键词
        achievement_keywords = [
            "突破", "首次", "创新", "领先", "最新", "发布", "推出", "升级", "改进"
        ]
        
        for keyword in achievement_keywords:
            if keyword in content:
                analysis["achievements"].append(keyword)
        
        return analysis
    
    def _generate_title(self, original_title: str, analysis: Dict, style: str) -> str:
        """生成新标题"""
        if style == "wechat":
            # 提取关键信息
            topic = analysis.get("main_topic", "AI")
            companies = analysis.get("companies", [])
            achievements = analysis.get("achievements", [])
            
            # 构建关键点
            key_point = ""
            if companies:
                key_point = f"{companies[0]}有大动作"
            elif achievements:
                key_point = f"实现{achievements[0]}"
            else:
                key_point = "值得关注"
            
            # 选择模板
            import random
            template = random.choice(self.wechat_title_templates)
            new_title = template.format(topic=topic, key_point=key_point)
            
            # 长度控制
            if len(new_title) > 30:
                new_title = f"🔥 {topic}新突破：{key_point}"
            
            return new_title
        
        else:
            # 其他风格的标题生成
            return f"深度解析：{original_title}"
    
    def _rewrite_content(
        self, 
        content: str, 
        analysis: Dict, 
        style: str, 
        target_length: int,
        tone: str
    ) -> str:
        """改写内容主体"""
        
        # 构建改写提示词
        rewrite_prompt = self._build_rewrite_prompt(content, analysis, style, tone)
        
        # 这里应该调用LLM进行改写，但由于没有具体的LLM实例，我们使用规则改写
        rewritten = self._rule_based_rewrite(content, analysis, style, tone)
        
        # 长度调整
        if len(rewritten.replace(" ", "")) > target_length * 1.2:
            rewritten = self._truncate_content(rewritten, target_length)
        elif len(rewritten.replace(" ", "")) < target_length * 0.8:
            rewritten = self._expand_content(rewritten, analysis, target_length)
        
        return rewritten
    
    def _build_rewrite_prompt(self, content: str, analysis: Dict, style: str, tone: str) -> str:
        """构建LLM改写提示词"""
        prompt = f"""
请将以下技术性AI资讯改写为适合微信公众号发布的文章：

原始内容：
{content[:1000]}...

改写要求：
1. 风格：{style}
2. 语调：{tone}
3. 目标受众：对AI感兴趣的普通读者
4. 结构：引言 + 正文 + 总结
5. 语言：通俗易懂，避免过多技术术语
6. 长度：1500字左右

主要话题：{analysis.get('main_topic', 'AI')}
涉及公司：{', '.join(analysis.get('companies', []))}

请确保：
- 开头要吸引读者注意力
- 用简单的语言解释技术概念
- 突出这个发展的意义和影响
- 结尾要有总结和展望
"""
        return prompt
    
    def _rule_based_rewrite(self, content: str, analysis: Dict, style: str, tone: str) -> str:
        """基于规则的改写（LLM的简化版本）"""
        
        # 分段处理
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        rewritten_parts = []
        
        # 1. 引言部分
        intro = self._create_introduction(analysis, tone)
        rewritten_parts.append(intro)
        
        # 2. 正文部分
        for i, paragraph in enumerate(paragraphs[:5]):  # 限制段落数量
            rewritten_para = self._rewrite_paragraph(paragraph, analysis, tone)
            rewritten_parts.append(rewritten_para)
        
        # 3. 总结部分
        conclusion = self._create_conclusion(analysis, tone)
        rewritten_parts.append(conclusion)
        
        return '\n\n'.join(rewritten_parts)
    
    def _create_introduction(self, analysis: Dict, tone: str) -> str:
        """创建引言"""
        topic = analysis.get("main_topic", "AI")
        companies = analysis.get("companies", [])
        
        if tone == "enthusiastic":
            if companies:
                return f"🎉 大新闻！{companies[0]}在{topic}领域又有重大突破了！这次的发展可能会彻底改变我们对{topic}的认知。让我们一起来看看到底发生了什么吧！"
            else:
                return f"🚀 {topic}领域传来振奋人心的消息！最新的技术突破让人眼前一亮，这可能是我们见证历史的时刻。"
        
        elif tone == "professional":
            return f"近期，{topic}领域出现了值得关注的新发展。本文将为您详细解析这一技术进步的背景、意义及其可能带来的影响。"
        
        else:  # casual
            if companies:
                return f"最近{companies[0]}在{topic}方面有了新动作，这个消息在科技圈引起了不小的关注。今天我们就来聊聊这到底是怎么回事。"
            else:
                return f"说到{topic}，最近又有新的进展了。虽然听起来很技术，但其实和我们的生活息息相关。"
    
    def _rewrite_paragraph(self, paragraph: str, analysis: Dict, tone: str) -> str:
        """改写段落"""
        # 简化技术术语
        simplified = self._simplify_technical_terms(paragraph)
        
        # 添加解释
        explained = self._add_explanations(simplified)
        
        # 调整语调
        adjusted = self._adjust_tone(explained, tone)
        
        return adjusted
    
    def _simplify_technical_terms(self, text: str) -> str:
        """简化技术术语"""
        replacements = {
            "large language model": "大语言模型（就像ChatGPT那样的AI）",
            "neural network": "神经网络（模拟人脑的AI系统）",
            "machine learning": "机器学习（让计算机自己学习的技术）",
            "deep learning": "深度学习（更高级的机器学习）",
            "natural language processing": "自然语言处理（让AI理解人类语言）",
            "computer vision": "计算机视觉（让AI能够'看'图像）",
            "algorithm": "算法（计算机解决问题的方法）",
            "dataset": "数据集（用来训练AI的大量数据）",
            "training": "训练（教AI学习的过程）",
            "inference": "推理（AI做出判断的过程）"
        }
        
        for term, explanation in replacements.items():
            if term in text.lower():
                text = text.replace(term, explanation)
        
        return text
    
    def _add_explanations(self, text: str) -> str:
        """添加解释"""
        # 这里可以添加更多的解释逻辑
        return text
    
    def _adjust_tone(self, text: str, tone: str) -> str:
        """调整语调"""
        if tone == "enthusiastic":
            # 添加感叹号和积极词汇
            text = text.replace("。", "！")
            text = text.replace("，", "，真的很")
        elif tone == "casual":
            # 添加口语化表达
            text = text.replace("因此", "所以")
            text = text.replace("然而", "不过")
            text = text.replace("此外", "另外")
        
        return text
    
    def _create_conclusion(self, analysis: Dict, tone: str) -> str:
        """创建总结"""
        topic = analysis.get("main_topic", "AI")
        
        if tone == "enthusiastic":
            return f"🌟 总的来说，{topic}的这次发展真的让人兴奋！虽然我们还不知道最终会带来什么样的变化，但可以肯定的是，未来会更加精彩。你觉得呢？"
        
        elif tone == "professional":
            return f"综上所述，{topic}领域的这一进展具有重要意义。虽然仍需时间验证其长期影响，但无疑为行业发展指明了新的方向。"
        
        else:  # casual
            return f"说了这么多，其实{topic}的发展就是在让我们的生活变得更便利。虽然现在可能还感受不到，但相信不久的将来，我们都会受益于这些技术进步。"
    
    def _add_interactions(self, content: str, style: str) -> str:
        """添加互动元素"""
        if style != "wechat":
            return content
        
        import random
        
        # 在适当位置添加互动元素
        paragraphs = content.split('\n\n')
        
        # 在中间添加问题
        if len(paragraphs) > 3:
            question = random.choice(self.interaction_templates["question"])
            paragraphs.insert(len(paragraphs)//2, f"🤔 {question}")
        
        # 在结尾添加行动号召
        cta = random.choice(self.interaction_templates["call_to_action"])
        engagement = random.choice(self.interaction_templates["engagement"])
        
        paragraphs.append(f"\n{cta}")
        paragraphs.append(f"{engagement}")
        
        return '\n\n'.join(paragraphs)
    
    def _generate_summary(self, content: str) -> str:
        """生成摘要"""
        # 简单的摘要生成
        sentences = content.split('。')[:3]
        return '。'.join(sentences) + '。'
    
    def _extract_key_points(self, content: str) -> List[str]:
        """提取关键点"""
        # 简单的关键点提取
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        key_points = []
        
        for para in paragraphs[:5]:
            if len(para) > 50:
                # 提取第一句作为关键点
                first_sentence = para.split('。')[0] + '。'
                key_points.append(first_sentence)
        
        return key_points[:3]
    
    def _truncate_content(self, content: str, target_length: int) -> str:
        """截断内容"""
        if len(content.replace(" ", "")) <= target_length:
            return content
        
        # 按段落截断
        paragraphs = content.split('\n\n')
        truncated = []
        current_length = 0
        
        for para in paragraphs:
            para_length = len(para.replace(" ", ""))
            if current_length + para_length <= target_length:
                truncated.append(para)
                current_length += para_length
            else:
                break
        
        return '\n\n'.join(truncated)
    
    def _expand_content(self, content: str, analysis: Dict, target_length: int) -> str:
        """扩展内容"""
        current_length = len(content.replace(" ", ""))
        if current_length >= target_length * 0.8:
            return content
        
        # 添加背景信息
        topic = analysis.get("main_topic", "AI")
        background = f"\n\n💡 背景知识：{topic}技术近年来发展迅速，已经在多个领域展现出巨大潜力。从智能助手到自动驾驶，从医疗诊断到金融分析，{topic}正在改变我们的生活方式。"
        
        # 添加影响分析
        impact = f"\n\n🔮 未来展望：这项技术的发展可能会带来深远影响。不仅会推动相关行业的进步，也可能创造出全新的商业模式和就业机会。当然，我们也需要关注其可能带来的挑战和风险。"
        
        return content + background + impact
