"""
网页内容抓取工具 - 解析HTML并提取文章内容
"""

import json
import requests
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from smolagents import Tool

from ..utils.logger import get_logger

logger = get_logger(__name__)


class ContentFetchTool(Tool):
    """
    网页内容抓取工具，能够解析HTML并提取文章内容
    
    功能：
    - 获取网页完整内容
    - 智能提取文章正文
    - 提取元数据（标题、作者、发布时间等）
    - 处理各种网站结构
    """
    
    name = "content_fetch"
    description = """
    从指定URL获取网页内容并提取文章信息。
    
    参数:
    - url (str): 要抓取的网页URL
    - extract_images (bool, 可选): 是否提取图片链接，默认True
    - extract_links (bool, 可选): 是否提取相关链接，默认False
    - clean_content (bool, 可选): 是否清理内容，默认True
    
    返回: 包含文章内容的JSON字符串，包含title, content, author, published_date, images等
    """
    
    inputs = {
        "url": {
            "type": "string",
            "description": "要抓取的网页URL"
        },
        "extract_images": {
            "type": "boolean",
            "description": "是否提取图片链接",
            "default": True
        },
        "extract_links": {
            "type": "boolean", 
            "description": "是否提取相关链接",
            "default": False
        },
        "clean_content": {
            "type": "boolean",
            "description": "是否清理内容",
            "default": True
        }
    }
    
    output_type = "string"
    
    def __init__(self):
        super().__init__()
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
        
        # 常见的文章内容选择器
        self.content_selectors = [
            "article",
            ".article-content",
            ".post-content", 
            ".entry-content",
            ".content",
            ".main-content",
            "#content",
            ".article-body",
            ".post-body",
            ".story-body",
            "[role='main']"
        ]
        
        # 需要移除的元素选择器
        self.remove_selectors = [
            "script", "style", "nav", "header", "footer",
            ".advertisement", ".ads", ".sidebar", ".comments",
            ".social-share", ".related-posts", ".newsletter",
            ".popup", ".modal", ".cookie-notice"
        ]
    
    def forward(
        self,
        url: str,
        extract_images: bool = True,
        extract_links: bool = False,
        clean_content: bool = True
    ) -> str:
        """抓取网页内容"""
        try:
            logger.info(f"开始抓取网页: {url}")
            
            # 获取网页内容
            response = requests.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()
            response.encoding = response.apparent_encoding or 'utf-8'
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'lxml')
            
            # 提取基本信息
            result = {
                "url": url,
                "title": self._extract_title(soup),
                "author": self._extract_author(soup),
                "published_date": self._extract_published_date(soup),
                "description": self._extract_description(soup),
                "content": self._extract_content(soup, clean_content),
                "word_count": 0,
                "images": [],
                "links": [],
                "language": self._detect_language(soup),
                "success": True
            }
            
            # 计算字数
            if result["content"]:
                result["word_count"] = len(result["content"].replace(" ", ""))
            
            # 提取图片
            if extract_images:
                result["images"] = self._extract_images(soup, url)
            
            # 提取链接
            if extract_links:
                result["links"] = self._extract_links(soup, url)
            
            logger.info(f"抓取完成: {result['title']}, 字数: {result['word_count']}")
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"抓取失败: {str(e)}")
            return json.dumps({
                "url": url,
                "error": f"抓取失败: {str(e)}",
                "success": False
            }, ensure_ascii=False)
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取标题"""
        # 尝试多种标题提取方法
        selectors = [
            "h1",
            ".article-title",
            ".post-title",
            ".entry-title",
            "title",
            "[property='og:title']",
            "[name='twitter:title']"
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                title = element.get_text(strip=True) if hasattr(element, 'get_text') else element.get('content', '')
                if title and len(title) > 5:
                    return title
        
        return "未找到标题"
    
    def _extract_author(self, soup: BeautifulSoup) -> Optional[str]:
        """提取作者"""
        selectors = [
            ".author",
            ".byline",
            "[rel='author']",
            "[property='article:author']",
            "[name='author']",
            ".post-author",
            ".article-author"
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                author = element.get_text(strip=True) if hasattr(element, 'get_text') else element.get('content', '')
                if author:
                    return author
        
        return None
    
    def _extract_published_date(self, soup: BeautifulSoup) -> Optional[str]:
        """提取发布时间"""
        selectors = [
            "[property='article:published_time']",
            "[property='article:modified_time']", 
            "[name='publish_date']",
            "[name='date']",
            "time[datetime]",
            ".publish-date",
            ".post-date",
            ".article-date"
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                date = element.get('datetime') or element.get('content') or element.get_text(strip=True)
                if date:
                    return date
        
        return None
    
    def _extract_description(self, soup: BeautifulSoup) -> Optional[str]:
        """提取描述"""
        selectors = [
            "[property='og:description']",
            "[name='description']",
            "[name='twitter:description']",
            ".article-summary",
            ".post-excerpt"
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                desc = element.get('content') or element.get_text(strip=True)
                if desc and len(desc) > 10:
                    return desc
        
        return None
    
    def _extract_content(self, soup: BeautifulSoup, clean_content: bool) -> str:
        """提取正文内容"""
        # 移除不需要的元素
        if clean_content:
            for selector in self.remove_selectors:
                for element in soup.select(selector):
                    element.decompose()
        
        # 尝试找到主要内容区域
        content_element = None
        for selector in self.content_selectors:
            element = soup.select_one(selector)
            if element:
                content_element = element
                break
        
        # 如果没找到特定的内容区域，使用整个body
        if not content_element:
            content_element = soup.find('body') or soup
        
        # 提取文本内容
        if content_element:
            # 获取所有段落文本
            paragraphs = []
            for p in content_element.find_all(['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                text = p.get_text(strip=True)
                if text and len(text) > 20:  # 过滤太短的段落
                    paragraphs.append(text)
            
            content = '\n\n'.join(paragraphs)
            
            # 清理内容
            if clean_content:
                content = self._clean_text(content)
            
            return content
        
        return ""
    
    def _extract_images(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """提取图片"""
        images = []
        
        for img in soup.find_all('img'):
            src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
            if src:
                # 转换为绝对URL
                absolute_url = urljoin(base_url, src)
                
                image_info = {
                    "url": absolute_url,
                    "alt": img.get('alt', ''),
                    "title": img.get('title', ''),
                    "width": img.get('width', ''),
                    "height": img.get('height', '')
                }
                images.append(image_info)
        
        return images[:10]  # 限制图片数量
    
    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """提取链接"""
        links = []
        
        for a in soup.find_all('a', href=True):
            href = a['href']
            text = a.get_text(strip=True)
            
            if href and text and len(text) > 3:
                # 转换为绝对URL
                absolute_url = urljoin(base_url, href)
                
                link_info = {
                    "url": absolute_url,
                    "text": text,
                    "title": a.get('title', '')
                }
                links.append(link_info)
        
        return links[:20]  # 限制链接数量
    
    def _detect_language(self, soup: BeautifulSoup) -> str:
        """检测语言"""
        # 检查html标签的lang属性
        html_tag = soup.find('html')
        if html_tag and html_tag.get('lang'):
            return html_tag['lang']
        
        # 检查meta标签
        lang_meta = soup.find('meta', attrs={'name': 'language'})
        if lang_meta and lang_meta.get('content'):
            return lang_meta['content']
        
        # 简单的中文检测
        text_sample = soup.get_text()[:1000]
        chinese_chars = len([c for c in text_sample if '\u4e00' <= c <= '\u9fff'])
        if chinese_chars > len(text_sample) * 0.1:
            return 'zh-CN'
        
        return 'en'
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        import re
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除多余的换行
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef.,!?;:()[\]{}"\'-]', '', text)
        
        return text.strip()
