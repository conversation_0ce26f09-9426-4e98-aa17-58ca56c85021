"""
文本处理工具 - 文本清洗、关键信息提取、关键词识别等
"""

import json
import re
from typing import List, Dict, Any, Optional, Tuple
from collections import Counter
from smolagents import Tool

from ..utils.logger import get_logger

logger = get_logger(__name__)


class TextProcessingTool(Tool):
    """
    文本处理工具，提供文本清洗、关键信息提取、关键词识别等功能
    
    功能：
    - 文本清洗和标准化
    - 关键词提取
    - 摘要生成
    - 情感分析
    - 文本统计
    """
    
    name = "text_processing"
    description = """
    对文本进行处理和分析，包括清洗、关键词提取、摘要等。
    
    参数:
    - text (str): 要处理的文本内容
    - operation (str): 操作类型，支持 'clean', 'keywords', 'summary', 'sentiment', 'stats'
    - max_keywords (int, 可选): 最大关键词数量，默认10
    - summary_ratio (float, 可选): 摘要比例，默认0.3
    
    返回: 包含处理结果的JSON字符串
    """
    
    inputs = {
        "text": {
            "type": "string",
            "description": "要处理的文本内容"
        },
        "operation": {
            "type": "string",
            "description": "操作类型：clean, keywords, summary, sentiment, stats, all",
            "default": "all",
            "nullable": True
        },
        "max_keywords": {
            "type": "integer",
            "description": "最大关键词数量",
            "default": 10,
            "nullable": True
        },
        "summary_ratio": {
            "type": "number",
            "description": "摘要比例（0.1-0.5）",
            "default": 0.3,
            "nullable": True
        }
    }
    
    output_type = "string"
    
    def __init__(self):
        super().__init__()
        
        # 中文停用词
        self.chinese_stopwords = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '里', '就是', '可以', '这个', '但是', '他们', '我们',
            '如果', '什么', '因为', '所以', '虽然', '但是', '然后', '还是', '或者',
            '以及', '以上', '以下', '关于', '对于', '由于', '根据', '通过', '为了'
        }
        
        # 英文停用词
        self.english_stopwords = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of',
            'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
            'those', 'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves',
            'you', 'your', 'yours', 'yourself', 'yourselves', 'he', 'him', 'his',
            'himself', 'she', 'her', 'hers', 'herself', 'it', 'its', 'itself',
            'they', 'them', 'their', 'theirs', 'themselves', 'what', 'which', 'who',
            'whom', 'whose', 'this', 'that', 'these', 'those', 'am', 'is', 'are',
            'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'having',
            'do', 'does', 'did', 'doing', 'will', 'would', 'could', 'should', 'may',
            'might', 'must', 'can', 'shall'
        }
        
        # AI相关关键词权重
        self.ai_keywords_weight = {
            'ai', 'artificial intelligence', '人工智能', 'machine learning', '机器学习',
            'deep learning', '深度学习', 'neural network', '神经网络', 'gpt', 'llm',
            'large language model', '大语言模型', 'chatgpt', 'openai', 'google',
            'microsoft', 'anthropic', 'claude', 'transformer', 'bert', 'nlp',
            'natural language processing', '自然语言处理', 'computer vision',
            '计算机视觉', 'robotics', '机器人', 'automation', '自动化'
        }
    
    def forward(
        self,
        text: str,
        operation: str = "all",
        max_keywords: int = 10,
        summary_ratio: float = 0.3
    ) -> str:
        """处理文本"""
        try:
            logger.info(f"开始文本处理，操作: {operation}, 文本长度: {len(text)}")
            
            result = {
                "original_length": len(text),
                "success": True
            }
            
            if operation == "clean" or operation == "all":
                result["cleaned_text"] = self._clean_text(text)
            
            if operation == "keywords" or operation == "all":
                result["keywords"] = self._extract_keywords(text, max_keywords)
            
            if operation == "summary" or operation == "all":
                result["summary"] = self._generate_summary(text, summary_ratio)
            
            if operation == "sentiment" or operation == "all":
                result["sentiment"] = self._analyze_sentiment(text)
            
            if operation == "stats" or operation == "all":
                result["statistics"] = self._calculate_statistics(text)
            
            logger.info("文本处理完成")
            return json.dumps(result, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"文本处理失败: {str(e)}")
            return json.dumps({
                "error": f"文本处理失败: {str(e)}",
                "success": False
            }, ensure_ascii=False)
    
    def _clean_text(self, text: str) -> str:
        """清洗文本"""
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 移除URL
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # 移除邮箱
        text = re.sub(r'\S+@\S+', '', text)
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除多余的换行
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # 移除特殊字符，保留中英文、数字和基本标点
        text = re.sub(r'[^\w\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef.,!?;:()[\]{}"\'-]', '', text)
        
        # 移除过短的行
        lines = text.split('\n')
        cleaned_lines = [line.strip() for line in lines if len(line.strip()) > 5]
        
        return '\n'.join(cleaned_lines).strip()
    
    def _extract_keywords(self, text: str, max_keywords: int) -> List[Dict[str, Any]]:
        """提取关键词"""
        # 清洗文本
        cleaned_text = self._clean_text(text).lower()
        
        # 分词（简单的基于空格和标点的分词）
        words = re.findall(r'\b\w+\b', cleaned_text)
        
        # 过滤停用词和短词
        filtered_words = []
        for word in words:
            if (len(word) > 2 and 
                word not in self.chinese_stopwords and 
                word not in self.english_stopwords):
                filtered_words.append(word)
        
        # 统计词频
        word_freq = Counter(filtered_words)
        
        # 计算权重（考虑AI相关词汇）
        weighted_words = {}
        for word, freq in word_freq.items():
            weight = freq
            
            # AI相关词汇加权
            for ai_keyword in self.ai_keywords_weight:
                if ai_keyword.lower() in word or word in ai_keyword.lower():
                    weight *= 2
                    break
            
            # 长词加权
            if len(word) > 6:
                weight *= 1.2
            
            weighted_words[word] = weight
        
        # 排序并返回前N个
        sorted_keywords = sorted(weighted_words.items(), key=lambda x: x[1], reverse=True)
        
        keywords = []
        for word, weight in sorted_keywords[:max_keywords]:
            keywords.append({
                "word": word,
                "frequency": word_freq[word],
                "weight": round(weight, 2),
                "is_ai_related": any(ai_kw.lower() in word or word in ai_kw.lower() 
                                   for ai_kw in self.ai_keywords_weight)
            })
        
        return keywords
    
    def _generate_summary(self, text: str, ratio: float) -> str:
        """生成摘要（基于句子重要性）"""
        # 分句
        sentences = re.split(r'[.!?。！？]', text)
        sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
        
        if len(sentences) <= 3:
            return text
        
        # 计算句子重要性
        sentence_scores = {}
        
        # 提取关键词
        keywords = self._extract_keywords(text, 20)
        keyword_set = {kw["word"].lower() for kw in keywords}
        
        for i, sentence in enumerate(sentences):
            score = 0
            words = re.findall(r'\b\w+\b', sentence.lower())
            
            # 关键词匹配得分
            for word in words:
                if word in keyword_set:
                    score += 1
            
            # 位置得分（开头和结尾的句子更重要）
            if i < len(sentences) * 0.2:  # 前20%
                score += 2
            elif i > len(sentences) * 0.8:  # 后20%
                score += 1
            
            # 长度得分（适中长度的句子更重要）
            if 50 <= len(sentence) <= 200:
                score += 1
            
            sentence_scores[i] = score
        
        # 选择得分最高的句子
        num_sentences = max(1, int(len(sentences) * ratio))
        top_sentences = sorted(sentence_scores.items(), key=lambda x: x[1], reverse=True)[:num_sentences]
        
        # 按原顺序排列
        selected_indices = sorted([idx for idx, _ in top_sentences])
        summary_sentences = [sentences[i] for i in selected_indices]
        
        return '。'.join(summary_sentences) + '。'
    
    def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """简单的情感分析"""
        # 积极词汇
        positive_words = {
            '好', '棒', '优秀', '成功', '进步', '突破', '创新', '领先', '先进', '卓越',
            'good', 'great', 'excellent', 'success', 'progress', 'breakthrough',
            'innovation', 'leading', 'advanced', 'outstanding', 'amazing', 'wonderful'
        }
        
        # 消极词汇
        negative_words = {
            '坏', '差', '失败', '问题', '困难', '挑战', '风险', '担心', '危险', '错误',
            'bad', 'poor', 'failure', 'problem', 'difficult', 'challenge', 'risk',
            'worry', 'dangerous', 'error', 'concern', 'issue'
        }
        
        # 中性词汇
        neutral_words = {
            '发布', '宣布', '推出', '介绍', '展示', '研究', '开发', '测试', '分析',
            'release', 'announce', 'launch', 'introduce', 'show', 'research',
            'develop', 'test', 'analyze', 'study'
        }
        
        words = re.findall(r'\b\w+\b', text.lower())
        
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        neutral_count = sum(1 for word in words if word in neutral_words)
        
        total_sentiment_words = positive_count + negative_count + neutral_count
        
        if total_sentiment_words == 0:
            sentiment = "neutral"
            confidence = 0.5
        else:
            if positive_count > negative_count:
                sentiment = "positive"
                confidence = positive_count / total_sentiment_words
            elif negative_count > positive_count:
                sentiment = "negative"
                confidence = negative_count / total_sentiment_words
            else:
                sentiment = "neutral"
                confidence = neutral_count / total_sentiment_words
        
        return {
            "sentiment": sentiment,
            "confidence": round(confidence, 2),
            "positive_words": positive_count,
            "negative_words": negative_count,
            "neutral_words": neutral_count
        }
    
    def _calculate_statistics(self, text: str) -> Dict[str, Any]:
        """计算文本统计信息"""
        # 基本统计
        char_count = len(text)
        char_count_no_spaces = len(text.replace(' ', ''))
        word_count = len(re.findall(r'\b\w+\b', text))
        sentence_count = len(re.split(r'[.!?。！？]', text))
        paragraph_count = len([p for p in text.split('\n\n') if p.strip()])
        
        # 语言检测
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_words = len(re.findall(r'\b[a-zA-Z]+\b', text))
        
        if chinese_chars > english_words:
            primary_language = "Chinese"
        elif english_words > chinese_chars:
            primary_language = "English"
        else:
            primary_language = "Mixed"
        
        # 可读性评估（简化版）
        if sentence_count > 0:
            avg_sentence_length = word_count / sentence_count
            if avg_sentence_length < 15:
                readability = "Easy"
            elif avg_sentence_length < 25:
                readability = "Medium"
            else:
                readability = "Hard"
        else:
            readability = "Unknown"
        
        return {
            "character_count": char_count,
            "character_count_no_spaces": char_count_no_spaces,
            "word_count": word_count,
            "sentence_count": sentence_count,
            "paragraph_count": paragraph_count,
            "primary_language": primary_language,
            "chinese_character_count": chinese_chars,
            "english_word_count": english_words,
            "average_sentence_length": round(avg_sentence_length, 1) if sentence_count > 0 else 0,
            "readability": readability
        }
