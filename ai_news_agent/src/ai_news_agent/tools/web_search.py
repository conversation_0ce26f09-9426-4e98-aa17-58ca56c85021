"""
网络搜索工具 - 支持多个搜索引擎和AI专业网站搜索
"""

import json
import requests
from typing import List, Dict, Any, Optional
from urllib.parse import quote_plus
from smolagents import Tool

from ..utils.logger import get_logger

logger = get_logger(__name__)


class WebSearchTool(Tool):
    """
    网络搜索工具，支持多个搜索引擎和AI专业网站搜索
    
    支持的搜索源：
    - DuckDuckGo (默认)
    - Google (需要API密钥)
    - Bing (需要API密钥)
    - arXiv (学术论文)
    - AI专业网站
    """
    
    name = "web_search"
    description = """
    搜索最新的AI资讯和新闻。
    
    参数:
    - query (str): 搜索关键词
    - source (str, 可选): 搜索源，支持 'duckduckgo', 'google', 'bing', 'arxiv', 'ai_news'
    - max_results (int, 可选): 最大结果数量，默认10
    - time_filter (str, 可选): 时间过滤，支持 'day', 'week', 'month', 'year'
    
    返回: 包含搜索结果的JSON字符串，每个结果包含title, url, snippet, published_date
    """
    
    inputs = {
        "query": {
            "type": "string",
            "description": "搜索关键词，例如：'GPT-4 最新进展'"
        },
        "source": {
            "type": "string",
            "description": "搜索源：duckduckgo, google, bing, arxiv, ai_news",
            "default": "duckduckgo",
            "nullable": True
        },
        "max_results": {
            "type": "integer",
            "description": "最大结果数量",
            "default": 10,
            "nullable": True
        },
        "time_filter": {
            "type": "string",
            "description": "时间过滤：day, week, month, year",
            "default": "week",
            "nullable": True
        }
    }
    
    output_type = "string"
    
    def __init__(self):
        super().__init__()
        self.ai_news_sites = [
            "techcrunch.com/category/artificial-intelligence",
            "venturebeat.com/ai",
            "theverge.com/ai-artificial-intelligence",
            "arstechnica.com/ai",
            "wired.com/tag/artificial-intelligence",
            "mit.edu/news/topic/artificial-intelligence2",
            "openai.com/blog",
            "deepmind.google/discover/blog",
            "ai.googleblog.com",
            "blogs.microsoft.com/ai"
        ]
    
    def forward(
        self, 
        query: str, 
        source: str = "duckduckgo",
        max_results: int = 10,
        time_filter: str = "week"
    ) -> str:
        """执行网络搜索"""
        try:
            logger.info(f"开始搜索: {query}, 源: {source}, 最大结果: {max_results}")
            
            if source == "duckduckgo":
                results = self._search_duckduckgo(query, max_results, time_filter)
            elif source == "arxiv":
                results = self._search_arxiv(query, max_results)
            elif source == "ai_news":
                results = self._search_ai_news_sites(query, max_results, time_filter)
            else:
                # 默认使用DuckDuckGo
                results = self._search_duckduckgo(query, max_results, time_filter)
            
            logger.info(f"搜索完成，找到 {len(results)} 个结果")
            return json.dumps(results, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"搜索失败: {str(e)}")
            return json.dumps({
                "error": f"搜索失败: {str(e)}",
                "results": []
            }, ensure_ascii=False)
    
    def _search_duckduckgo(
        self, 
        query: str, 
        max_results: int,
        time_filter: str
    ) -> List[Dict[str, Any]]:
        """使用DuckDuckGo搜索"""
        try:
            # DuckDuckGo Instant Answer API
            url = "https://api.duckduckgo.com/"
            params = {
                "q": query,
                "format": "json",
                "no_html": "1",
                "skip_disambig": "1"
            }
            
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            results = []
            
            # 处理相关主题
            if "RelatedTopics" in data:
                for topic in data["RelatedTopics"][:max_results]:
                    if isinstance(topic, dict) and "Text" in topic:
                        result = {
                            "title": topic.get("Text", "")[:100] + "...",
                            "url": topic.get("FirstURL", ""),
                            "snippet": topic.get("Text", ""),
                            "published_date": None,
                            "source": "DuckDuckGo"
                        }
                        results.append(result)
            
            # 如果结果不够，使用备用搜索方法
            if len(results) < max_results:
                backup_results = self._search_duckduckgo_html(query, max_results - len(results))
                results.extend(backup_results)
            
            return results[:max_results]
            
        except Exception as e:
            logger.error(f"DuckDuckGo搜索失败: {str(e)}")
            return []
    
    def _search_duckduckgo_html(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """DuckDuckGo HTML搜索备用方法"""
        try:
            url = "https://html.duckduckgo.com/html/"
            params = {"q": query}
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            
            # 简单的HTML解析（这里可以用BeautifulSoup优化）
            results = []
            content = response.text
            
            # 这是一个简化的解析，实际项目中应该使用BeautifulSoup
            import re
            
            # 查找链接模式
            link_pattern = r'<a[^>]+href="([^"]+)"[^>]*>([^<]+)</a>'
            matches = re.findall(link_pattern, content)
            
            for i, (url, title) in enumerate(matches[:max_results]):
                if url.startswith("http") and len(title.strip()) > 10:
                    result = {
                        "title": title.strip(),
                        "url": url,
                        "snippet": f"来自DuckDuckGo的搜索结果: {title.strip()}",
                        "published_date": None,
                        "source": "DuckDuckGo"
                    }
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"DuckDuckGo HTML搜索失败: {str(e)}")
            return []
    
    def _search_arxiv(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """搜索arXiv学术论文"""
        try:
            url = "http://export.arxiv.org/api/query"
            params = {
                "search_query": f"all:{query}",
                "start": 0,
                "max_results": max_results,
                "sortBy": "submittedDate",
                "sortOrder": "descending"
            }
            
            response = requests.get(url, params=params, timeout=15)
            response.raise_for_status()
            
            # 解析XML响应
            import xml.etree.ElementTree as ET
            root = ET.fromstring(response.content)
            
            results = []
            namespace = {"atom": "http://www.w3.org/2005/Atom"}
            
            for entry in root.findall("atom:entry", namespace):
                title = entry.find("atom:title", namespace)
                summary = entry.find("atom:summary", namespace)
                published = entry.find("atom:published", namespace)
                link = entry.find("atom:id", namespace)
                
                if title is not None and link is not None:
                    result = {
                        "title": title.text.strip().replace("\n", " "),
                        "url": link.text.strip(),
                        "snippet": summary.text.strip().replace("\n", " ")[:300] + "..." if summary is not None else "",
                        "published_date": published.text.strip() if published is not None else None,
                        "source": "arXiv"
                    }
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"arXiv搜索失败: {str(e)}")
            return []
    
    def _search_ai_news_sites(
        self, 
        query: str, 
        max_results: int,
        time_filter: str
    ) -> List[Dict[str, Any]]:
        """搜索AI专业新闻网站"""
        results = []
        
        # 为每个AI新闻网站构建搜索查询
        for site in self.ai_news_sites[:5]:  # 限制搜索的网站数量
            try:
                site_query = f"site:{site} {query}"
                site_results = self._search_duckduckgo(site_query, 2, time_filter)
                
                for result in site_results:
                    result["source"] = f"AI News - {site}"
                    results.append(result)
                
                if len(results) >= max_results:
                    break
                    
            except Exception as e:
                logger.warning(f"搜索网站 {site} 失败: {str(e)}")
                continue
        
        return results[:max_results]
