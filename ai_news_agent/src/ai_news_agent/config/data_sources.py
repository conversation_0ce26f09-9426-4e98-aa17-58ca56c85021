"""
数据源配置模块
"""

from typing import Dict, List, Any
from dataclasses import dataclass


@dataclass
class DataSource:
    """数据源配置类"""
    name: str
    url: str
    source_type: str  # 'rss', 'api', 'web', 'arxiv'
    enabled: bool = True
    priority: int = 1  # 1-5, 5为最高优先级
    update_frequency: int = 60  # 更新频率（分钟）
    max_articles: int = 10
    keywords: List[str] = None
    headers: Dict[str, str] = None
    params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []
        if self.headers is None:
            self.headers = {}
        if self.params is None:
            self.params = {}


class DataSourceConfig:
    """数据源配置管理类"""
    
    def __init__(self):
        self.sources = self._init_default_sources()
    
    def _init_default_sources(self) -> List[DataSource]:
        """初始化默认数据源"""
        return [
            # 学术论文源
            DataSource(
                name="arXiv AI",
                url="http://export.arxiv.org/api/query",
                source_type="arxiv",
                priority=4,
                update_frequency=120,
                max_articles=15,
                keywords=["artificial intelligence", "machine learning", "deep learning", "neural network"],
                params={
                    "search_query": "cat:cs.AI OR cat:cs.LG OR cat:cs.CL OR cat:cs.CV",
                    "sortBy": "submittedDate",
                    "sortOrder": "descending"
                }
            ),
            
            # 科技新闻网站
            DataSource(
                name="TechCrunch AI",
                url="https://techcrunch.com/category/artificial-intelligence/feed/",
                source_type="rss",
                priority=5,
                update_frequency=30,
                max_articles=10,
                keywords=["AI", "artificial intelligence", "machine learning", "ChatGPT", "OpenAI"]
            ),
            
            DataSource(
                name="VentureBeat AI",
                url="https://venturebeat.com/ai/feed/",
                source_type="rss",
                priority=4,
                update_frequency=45,
                max_articles=8,
                keywords=["AI", "machine learning", "deep learning", "automation"]
            ),
            
            DataSource(
                name="The Verge AI",
                url="https://www.theverge.com/ai-artificial-intelligence/rss/index.xml",
                source_type="rss",
                priority=4,
                update_frequency=60,
                max_articles=8,
                keywords=["AI", "artificial intelligence", "technology"]
            ),
            
            DataSource(
                name="Ars Technica AI",
                url="https://arstechnica.com/tag/artificial-intelligence/feed/",
                source_type="rss",
                priority=3,
                update_frequency=90,
                max_articles=6,
                keywords=["AI", "machine learning", "technology"]
            ),
            
            DataSource(
                name="Wired AI",
                url="https://www.wired.com/tag/artificial-intelligence/feed/",
                source_type="rss",
                priority=3,
                update_frequency=120,
                max_articles=6,
                keywords=["AI", "artificial intelligence", "future"]
            ),
            
            # AI公司官方博客
            DataSource(
                name="OpenAI Blog",
                url="https://openai.com/blog/rss.xml",
                source_type="rss",
                priority=5,
                update_frequency=180,
                max_articles=5,
                keywords=["GPT", "ChatGPT", "AI research", "language model"]
            ),
            
            DataSource(
                name="Google AI Blog",
                url="https://ai.googleblog.com/feeds/posts/default",
                source_type="rss",
                priority=5,
                update_frequency=180,
                max_articles=5,
                keywords=["Google AI", "machine learning", "research"]
            ),
            
            DataSource(
                name="DeepMind Blog",
                url="https://deepmind.google/discover/blog/rss.xml",
                source_type="rss",
                priority=4,
                update_frequency=240,
                max_articles=5,
                keywords=["DeepMind", "AI research", "breakthrough"]
            ),
            
            DataSource(
                name="Microsoft AI Blog",
                url="https://blogs.microsoft.com/ai/feed/",
                source_type="rss",
                priority=4,
                update_frequency=180,
                max_articles=5,
                keywords=["Microsoft AI", "Azure AI", "Copilot"]
            ),
            
            DataSource(
                name="Anthropic News",
                url="https://www.anthropic.com/news",
                source_type="web",
                priority=4,
                update_frequency=360,
                max_articles=3,
                keywords=["Claude", "AI safety", "Anthropic"],
                headers={
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                }
            ),
            
            # 中文AI资讯源
            DataSource(
                name="机器之心",
                url="https://www.jiqizhixin.com/rss",
                source_type="rss",
                priority=4,
                update_frequency=60,
                max_articles=8,
                keywords=["人工智能", "机器学习", "深度学习", "AI"]
            ),
            
            DataSource(
                name="AI科技评论",
                url="https://www.leiphone.com/category/ai/feed",
                source_type="rss",
                priority=3,
                update_frequency=90,
                max_articles=6,
                keywords=["人工智能", "AI", "科技"]
            ),
            
            # 研究机构
            DataSource(
                name="MIT AI News",
                url="https://news.mit.edu/topic/artificial-intelligence2-rss.xml",
                source_type="rss",
                priority=4,
                update_frequency=240,
                max_articles=5,
                keywords=["MIT", "AI research", "artificial intelligence"]
            ),
            
            DataSource(
                name="Stanford AI News",
                url="https://hai.stanford.edu/news/rss.xml",
                source_type="rss",
                priority=4,
                update_frequency=240,
                max_articles=5,
                keywords=["Stanford", "AI research", "HAI"]
            ),
            
            # 专业AI网站
            DataSource(
                name="AI News",
                url="https://artificialintelligence-news.com/feed/",
                source_type="rss",
                priority=3,
                update_frequency=120,
                max_articles=6,
                keywords=["AI news", "artificial intelligence", "technology"]
            ),
            
            DataSource(
                name="Towards Data Science",
                url="https://towardsdatascience.com/feed",
                source_type="rss",
                priority=3,
                update_frequency=60,
                max_articles=8,
                keywords=["data science", "machine learning", "AI"]
            )
        ]
    
    def get_enabled_sources(self) -> List[DataSource]:
        """获取启用的数据源"""
        return [source for source in self.sources if source.enabled]
    
    def get_sources_by_priority(self, min_priority: int = 1) -> List[DataSource]:
        """按优先级获取数据源"""
        filtered = [s for s in self.sources if s.enabled and s.priority >= min_priority]
        return sorted(filtered, key=lambda x: x.priority, reverse=True)
    
    def get_sources_by_type(self, source_type: str) -> List[DataSource]:
        """按类型获取数据源"""
        return [s for s in self.sources if s.enabled and s.source_type == source_type]
    
    def add_source(self, source: DataSource) -> None:
        """添加数据源"""
        self.sources.append(source)
    
    def remove_source(self, name: str) -> bool:
        """移除数据源"""
        for i, source in enumerate(self.sources):
            if source.name == name:
                del self.sources[i]
                return True
        return False
    
    def update_source(self, name: str, **kwargs) -> bool:
        """更新数据源配置"""
        for source in self.sources:
            if source.name == name:
                for key, value in kwargs.items():
                    if hasattr(source, key):
                        setattr(source, key, value)
                return True
        return False
    
    def get_source(self, name: str) -> DataSource:
        """获取指定数据源"""
        for source in self.sources:
            if source.name == name:
                return source
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "sources": [
                {
                    "name": s.name,
                    "url": s.url,
                    "source_type": s.source_type,
                    "enabled": s.enabled,
                    "priority": s.priority,
                    "update_frequency": s.update_frequency,
                    "max_articles": s.max_articles,
                    "keywords": s.keywords,
                    "headers": s.headers,
                    "params": s.params
                }
                for s in self.sources
            ]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DataSourceConfig':
        """从字典创建配置"""
        config = cls()
        config.sources = []
        
        for source_data in data.get("sources", []):
            source = DataSource(**source_data)
            config.sources.append(source)
        
        return config
