"""
输出配置模块
"""

import os
from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class OutputConfig:
    """输出配置类"""
    
    default_format: str = "both"  # markdown, wechat, both
    output_path: str = "output"
    save_original: bool = True
    save_rewritten: bool = True
    save_formatted: bool = True
    
    # 文件命名
    filename_template: str = "{date}_{title}_{format}"
    date_format: str = "%Y%m%d_%H%M%S"
    
    # 格式化选项
    add_images: bool = True
    add_seo: bool = True
    add_metadata: bool = True
    
    # 微信公众号特定选项
    wechat_style: str = "casual"  # professional, casual, enthusiastic
    wechat_add_interactions: bool = True
    wechat_target_length: int = 1500
    
    @classmethod
    def create_default(cls) -> 'OutputConfig':
        """创建默认配置"""
        return cls()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'OutputConfig':
        """从字典创建配置"""
        return cls(
            default_format=data.get('default_format', 'both'),
            output_path=data.get('output_path', 'output'),
            save_original=data.get('save_original', True),
            save_rewritten=data.get('save_rewritten', True),
            save_formatted=data.get('save_formatted', True),
            filename_template=data.get('filename_template', '{date}_{title}_{format}'),
            date_format=data.get('date_format', '%Y%m%d_%H%M%S'),
            add_images=data.get('add_images', True),
            add_seo=data.get('add_seo', True),
            add_metadata=data.get('add_metadata', True),
            wechat_style=data.get('wechat_style', 'casual'),
            wechat_add_interactions=data.get('wechat_add_interactions', True),
            wechat_target_length=data.get('wechat_target_length', 1500)
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'default_format': self.default_format,
            'output_path': self.output_path,
            'save_original': self.save_original,
            'save_rewritten': self.save_rewritten,
            'save_formatted': self.save_formatted,
            'filename_template': self.filename_template,
            'date_format': self.date_format,
            'add_images': self.add_images,
            'add_seo': self.add_seo,
            'add_metadata': self.add_metadata,
            'wechat_style': self.wechat_style,
            'wechat_add_interactions': self.wechat_add_interactions,
            'wechat_target_length': self.wechat_target_length
        }
    
    def update_from_env(self) -> None:
        """从环境变量更新配置"""
        
        # 输出路径
        output_path = os.getenv('AI_NEWS_AGENT_OUTPUT_PATH')
        if output_path:
            self.output_path = output_path
        
        # 默认格式
        default_format = os.getenv('AI_NEWS_AGENT_OUTPUT_FORMAT')
        if default_format:
            self.default_format = default_format
