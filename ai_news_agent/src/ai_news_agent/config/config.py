"""
主配置管理模块
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict

from .data_sources import DataSourceConfig
from .llm_config import LLMConfig
from .output_config import OutputConfig


@dataclass
class Config:
    """主配置类"""
    
    # LLM配置
    llm: LLMConfig
    
    # 数据源配置
    data_sources: DataSourceConfig
    
    # 输出配置
    output: OutputConfig
    
    # 应用配置
    app_name: str = "AI News Agent"
    version: str = "0.1.0"
    debug: bool = False
    log_level: str = "INFO"
    
    # 处理配置
    max_articles_per_run: int = 10
    default_time_filter: str = "recent"
    enable_batch_processing: bool = True
    
    @classmethod
    def load_from_file(cls, config_path: str) -> 'Config':
        """从文件加载配置"""
        
        if not os.path.exists(config_path):
            # 创建默认配置
            config = cls.create_default()
            config.save_to_file(config_path)
            return config
        
        with open(config_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        return cls.from_dict(data)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Config':
        """从字典创建配置"""
        
        # 解析LLM配置
        llm_data = data.get('llm', {})
        llm_config = LLMConfig.from_dict(llm_data)
        
        # 解析数据源配置
        data_sources_data = data.get('data_sources', {})
        data_sources_config = DataSourceConfig.from_dict(data_sources_data)
        
        # 解析输出配置
        output_data = data.get('output', {})
        output_config = OutputConfig.from_dict(output_data)
        
        # 创建主配置
        config = cls(
            llm=llm_config,
            data_sources=data_sources_config,
            output=output_config,
            app_name=data.get('app_name', 'AI News Agent'),
            version=data.get('version', '0.1.0'),
            debug=data.get('debug', False),
            log_level=data.get('log_level', 'INFO'),
            max_articles_per_run=data.get('max_articles_per_run', 10),
            default_time_filter=data.get('default_time_filter', 'recent'),
            enable_batch_processing=data.get('enable_batch_processing', True)
        )
        
        return config
    
    @classmethod
    def create_default(cls) -> 'Config':
        """创建默认配置"""
        
        return cls(
            llm=LLMConfig.create_default(),
            data_sources=DataSourceConfig(),
            output=OutputConfig.create_default()
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        
        return {
            'app_name': self.app_name,
            'version': self.version,
            'debug': self.debug,
            'log_level': self.log_level,
            'max_articles_per_run': self.max_articles_per_run,
            'default_time_filter': self.default_time_filter,
            'enable_batch_processing': self.enable_batch_processing,
            'llm': self.llm.to_dict(),
            'data_sources': self.data_sources.to_dict(),
            'output': self.output.to_dict()
        }
    
    def save_to_file(self, config_path: str) -> None:
        """保存到文件"""
        
        # 确保目录存在
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.to_dict(), f, default_flow_style=False, allow_unicode=True)
    
    def get_config_dir(self) -> str:
        """获取配置目录"""
        
        # 优先使用环境变量
        config_dir = os.getenv('AI_NEWS_AGENT_CONFIG_DIR')
        if config_dir:
            return config_dir
        
        # 使用用户主目录
        home_dir = Path.home()
        config_dir = home_dir / '.ai_news_agent'
        config_dir.mkdir(exist_ok=True)
        
        return str(config_dir)
    
    def get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        
        config_dir = self.get_config_dir()
        return os.path.join(config_dir, 'config.yaml')
    
    def update_from_env(self) -> None:
        """从环境变量更新配置"""
        
        # 更新日志级别
        log_level = os.getenv('AI_NEWS_AGENT_LOG_LEVEL')
        if log_level:
            self.log_level = log_level
        
        # 更新调试模式
        debug = os.getenv('AI_NEWS_AGENT_DEBUG')
        if debug:
            self.debug = debug.lower() in ('true', '1', 'yes')
        
        # 更新LLM配置
        self.llm.update_from_env()
        
        # 更新输出配置
        self.output.update_from_env()


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path
        self._config = None
    
    @property
    def config(self) -> Config:
        """获取配置"""
        
        if self._config is None:
            self.load_config()
        
        return self._config
    
    def load_config(self) -> None:
        """加载配置"""
        
        if self.config_path is None:
            # 使用默认配置路径
            default_config = Config.create_default()
            self.config_path = default_config.get_default_config_path()
        
        self._config = Config.load_from_file(self.config_path)
        
        # 从环境变量更新配置
        self._config.update_from_env()
    
    def save_config(self) -> None:
        """保存配置"""
        
        if self._config and self.config_path:
            self._config.save_to_file(self.config_path)
    
    def reload_config(self) -> None:
        """重新加载配置"""
        
        self._config = None
        self.load_config()
    
    def update_config(self, updates: Dict[str, Any]) -> None:
        """更新配置"""
        
        if self._config is None:
            self.load_config()
        
        # 简单的配置更新逻辑
        for key, value in updates.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        
        config = self.config
        
        return {
            'app_name': config.app_name,
            'version': config.version,
            'debug': config.debug,
            'log_level': config.log_level,
            'llm_provider': config.llm.provider,
            'llm_model': config.llm.model,
            'data_sources_count': len(config.data_sources.get_enabled_sources()),
            'output_format': config.output.default_format,
            'output_path': config.output.output_path
        }
