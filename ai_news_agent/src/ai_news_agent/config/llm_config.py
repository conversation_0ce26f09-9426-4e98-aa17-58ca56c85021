"""
LLM配置模块
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class LLMConfig:
    """LLM配置类"""
    
    provider: str = "openai"  # openai, anthropic, huggingface, local
    model: str = "gpt-3.5-turbo"
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    temperature: float = 0.7
    max_tokens: int = 2048
    timeout: int = 30
    
    # 特定提供商配置
    huggingface_model_id: Optional[str] = None
    local_model_path: Optional[str] = None
    
    @classmethod
    def create_default(cls) -> 'LLMConfig':
        """创建默认配置"""
        return cls()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LLMConfig':
        """从字典创建配置"""
        return cls(
            provider=data.get('provider', 'openai'),
            model=data.get('model', 'gpt-3.5-turbo'),
            api_key=data.get('api_key'),
            api_base=data.get('api_base'),
            temperature=data.get('temperature', 0.7),
            max_tokens=data.get('max_tokens', 2048),
            timeout=data.get('timeout', 30),
            huggingface_model_id=data.get('huggingface_model_id'),
            local_model_path=data.get('local_model_path')
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'provider': self.provider,
            'model': self.model,
            'api_key': self.api_key,
            'api_base': self.api_base,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens,
            'timeout': self.timeout,
            'huggingface_model_id': self.huggingface_model_id,
            'local_model_path': self.local_model_path
        }
    
    def update_from_env(self) -> None:
        """从环境变量更新配置"""
        
        # API密钥
        if self.provider == "openai":
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key:
                self.api_key = api_key
        elif self.provider == "anthropic":
            api_key = os.getenv('ANTHROPIC_API_KEY')
            if api_key:
                self.api_key = api_key
        elif self.provider == "huggingface":
            api_key = os.getenv('HUGGINGFACE_API_KEY')
            if api_key:
                self.api_key = api_key
        
        # 模型配置
        model = os.getenv('AI_NEWS_AGENT_MODEL')
        if model:
            self.model = model
        
        # 温度
        temperature = os.getenv('AI_NEWS_AGENT_TEMPERATURE')
        if temperature:
            try:
                self.temperature = float(temperature)
            except ValueError:
                pass
