"""
AI资讯获取模块 - 智能搜索策略实现
"""

import asyncio
import feedparser
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import urljoin
import xml.etree.ElementTree as ET

from ..config.data_sources import DataSourceConfig, DataSource
from ..tools.web_search import WebSearchTool
from ..tools.content_fetch import ContentFetchTool
from ..utils.logger import get_logger

logger = get_logger(__name__)


class SearchStrategy:
    """智能搜索策略类"""
    
    def __init__(self):
        self.ai_keywords = [
            # 英文关键词
            "artificial intelligence", "AI", "machine learning", "deep learning",
            "neural network", "GPT", "ChatGPT", "LLM", "large language model",
            "OpenAI", "Google AI", "Microsoft AI", "DeepMind", "Anthropic",
            "computer vision", "natural language processing", "NLP", "robotics",
            "automation", "AGI", "artificial general intelligence",
            
            # 中文关键词
            "人工智能", "机器学习", "深度学习", "神经网络", "大语言模型",
            "自然语言处理", "计算机视觉", "机器人", "自动化", "智能助手"
        ]
        
        self.trending_topics = [
            "GPT-4", "Claude", "Gemini", "Llama", "ChatGPT", "Copilot",
            "AI safety", "AI ethics", "multimodal AI", "AI agents",
            "generative AI", "foundation models", "transformer"
        ]
        
        self.time_filters = {
            "latest": 1,      # 最近1天
            "recent": 7,      # 最近1周
            "weekly": 30,     # 最近1月
            "monthly": 90     # 最近3月
        }
    
    def generate_search_queries(
        self, 
        topic: Optional[str] = None,
        time_filter: str = "recent",
        max_queries: int = 10
    ) -> List[Dict[str, Any]]:
        """生成智能搜索查询"""
        
        queries = []
        
        if topic:
            # 基于指定主题生成查询
            queries.extend(self._generate_topic_queries(topic, time_filter))
        else:
            # 生成热门话题查询
            queries.extend(self._generate_trending_queries(time_filter))
        
        # 添加时间过滤
        for query in queries:
            query["time_filter"] = time_filter
            query["days_back"] = self.time_filters.get(time_filter, 7)
        
        return queries[:max_queries]
    
    def _generate_topic_queries(self, topic: str, time_filter: str) -> List[Dict[str, Any]]:
        """基于主题生成查询"""
        queries = []
        
        # 基础查询
        queries.append({
            "query": topic,
            "priority": 5,
            "source_preference": ["arxiv", "rss", "web"]
        })
        
        # 组合查询
        for keyword in self.ai_keywords[:5]:
            if keyword.lower() not in topic.lower():
                combined_query = f"{topic} {keyword}"
                queries.append({
                    "query": combined_query,
                    "priority": 3,
                    "source_preference": ["rss", "web"]
                })
        
        # 新闻类查询
        news_terms = ["news", "breakthrough", "release", "announcement", "update"]
        for term in news_terms[:3]:
            news_query = f"{topic} {term}"
            queries.append({
                "query": news_query,
                "priority": 4,
                "source_preference": ["rss", "web"]
            })
        
        return queries
    
    def _generate_trending_queries(self, time_filter: str) -> List[Dict[str, Any]]:
        """生成热门话题查询"""
        queries = []
        
        # 热门AI话题
        for topic in self.trending_topics[:8]:
            queries.append({
                "query": topic,
                "priority": 4,
                "source_preference": ["rss", "arxiv", "web"]
            })
        
        # 组合热门查询
        combinations = [
            "AI breakthrough", "AI research", "AI development",
            "machine learning news", "deep learning advances",
            "AI industry news", "AI startup", "AI funding"
        ]
        
        for combo in combinations[:5]:
            queries.append({
                "query": combo,
                "priority": 3,
                "source_preference": ["rss", "web"]
            })
        
        return queries


class NewsFetcher:
    """AI资讯获取器"""
    
    def __init__(self, config: Optional[DataSourceConfig] = None):
        self.config = config or DataSourceConfig()
        self.search_strategy = SearchStrategy()
        self.web_search_tool = WebSearchTool()
        self.content_fetch_tool = ContentFetchTool()
        
        # 请求会话
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        })
    
    async def fetch_latest_news(
        self,
        topic: Optional[str] = None,
        max_articles: int = 20,
        time_filter: str = "recent",
        min_priority: int = 3
    ) -> List[Dict[str, Any]]:
        """获取最新AI资讯"""
        
        logger.info(f"开始获取AI资讯，主题: {topic}, 最大数量: {max_articles}")
        
        all_articles = []
        
        # 1. 从RSS源获取
        rss_articles = await self._fetch_from_rss_sources(min_priority, max_articles // 2)
        all_articles.extend(rss_articles)
        
        # 2. 从arXiv获取学术论文
        if topic:
            arxiv_articles = await self._fetch_from_arxiv(topic, max_articles // 4)
            all_articles.extend(arxiv_articles)
        
        # 3. 智能搜索补充
        search_queries = self.search_strategy.generate_search_queries(topic, time_filter, 5)
        search_articles = await self._fetch_from_search(search_queries, max_articles // 4)
        all_articles.extend(search_articles)
        
        # 4. 去重和排序
        unique_articles = self._deduplicate_articles(all_articles)
        filtered_articles = self._filter_by_relevance(unique_articles, topic)
        sorted_articles = self._sort_by_importance(filtered_articles)
        
        logger.info(f"获取完成，共找到 {len(sorted_articles)} 篇相关文章")
        return sorted_articles[:max_articles]
    
    async def _fetch_from_rss_sources(self, min_priority: int, max_articles: int) -> List[Dict[str, Any]]:
        """从RSS源获取文章"""
        
        articles = []
        rss_sources = self.config.get_sources_by_type("rss")
        high_priority_sources = [s for s in rss_sources if s.priority >= min_priority]
        
        for source in high_priority_sources[:10]:  # 限制源数量
            try:
                logger.info(f"正在获取RSS源: {source.name}")
                
                response = self.session.get(source.url, timeout=15)
                response.raise_for_status()
                
                feed = feedparser.parse(response.content)
                
                for entry in feed.entries[:source.max_articles]:
                    article = self._parse_rss_entry(entry, source)
                    if article and self._is_recent_article(article.get("published_date")):
                        articles.append(article)
                
            except Exception as e:
                logger.warning(f"RSS源 {source.name} 获取失败: {str(e)}")
                continue
        
        return articles[:max_articles]
    
    async def _fetch_from_arxiv(self, topic: str, max_articles: int) -> List[Dict[str, Any]]:
        """从arXiv获取学术论文"""
        
        articles = []
        arxiv_sources = self.config.get_sources_by_type("arxiv")
        
        for source in arxiv_sources:
            try:
                logger.info(f"正在获取arXiv论文: {topic}")
                
                # 构建搜索查询
                search_query = f"all:{topic}"
                params = {
                    "search_query": search_query,
                    "start": 0,
                    "max_results": max_articles,
                    "sortBy": "submittedDate",
                    "sortOrder": "descending"
                }
                
                response = self.session.get(source.url, params=params, timeout=20)
                response.raise_for_status()
                
                # 解析XML
                root = ET.fromstring(response.content)
                namespace = {"atom": "http://www.w3.org/2005/Atom"}
                
                for entry in root.findall("atom:entry", namespace):
                    article = self._parse_arxiv_entry(entry, namespace, source)
                    if article:
                        articles.append(article)
                
            except Exception as e:
                logger.warning(f"arXiv获取失败: {str(e)}")
                continue
        
        return articles[:max_articles]
    
    async def _fetch_from_search(self, queries: List[Dict[str, Any]], max_articles: int) -> List[Dict[str, Any]]:
        """通过搜索获取文章"""
        
        articles = []
        
        for query_info in queries:
            try:
                query = query_info["query"]
                time_filter = query_info.get("time_filter", "week")
                
                logger.info(f"正在搜索: {query}")
                
                # 使用WebSearchTool搜索
                search_result = self.web_search_tool.forward(
                    query=query,
                    source="duckduckgo",
                    max_results=5,
                    time_filter=time_filter
                )
                
                import json
                search_data = json.loads(search_result)
                
                if isinstance(search_data, list):
                    for item in search_data:
                        article = self._parse_search_result(item, query_info)
                        if article:
                            articles.append(article)
                
            except Exception as e:
                logger.warning(f"搜索查询 {query_info['query']} 失败: {str(e)}")
                continue
        
        return articles[:max_articles]
    
    def _parse_rss_entry(self, entry: Any, source: DataSource) -> Optional[Dict[str, Any]]:
        """解析RSS条目"""
        
        try:
            # 检查关键词匹配
            title = getattr(entry, 'title', '')
            summary = getattr(entry, 'summary', '')
            content = f"{title} {summary}".lower()
            
            if source.keywords and not any(kw.lower() in content for kw in source.keywords):
                return None
            
            article = {
                "title": title,
                "url": getattr(entry, 'link', ''),
                "summary": summary,
                "published_date": self._parse_date(getattr(entry, 'published', '')),
                "author": getattr(entry, 'author', ''),
                "source": source.name,
                "source_type": "rss",
                "priority": source.priority,
                "keywords": source.keywords,
                "content": ""  # 将在后续获取
            }
            
            return article
            
        except Exception as e:
            logger.warning(f"解析RSS条目失败: {str(e)}")
            return None
    
    def _parse_arxiv_entry(self, entry: Any, namespace: Dict, source: DataSource) -> Optional[Dict[str, Any]]:
        """解析arXiv条目"""
        
        try:
            title_elem = entry.find("atom:title", namespace)
            summary_elem = entry.find("atom:summary", namespace)
            published_elem = entry.find("atom:published", namespace)
            id_elem = entry.find("atom:id", namespace)
            
            if title_elem is None or id_elem is None:
                return None
            
            title = title_elem.text.strip().replace("\n", " ")
            summary = summary_elem.text.strip().replace("\n", " ") if summary_elem is not None else ""
            
            article = {
                "title": title,
                "url": id_elem.text.strip(),
                "summary": summary,
                "published_date": published_elem.text.strip() if published_elem is not None else "",
                "author": "",
                "source": source.name,
                "source_type": "arxiv",
                "priority": source.priority,
                "keywords": source.keywords,
                "content": summary
            }
            
            return article
            
        except Exception as e:
            logger.warning(f"解析arXiv条目失败: {str(e)}")
            return None
    
    def _parse_search_result(self, result: Dict[str, Any], query_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析搜索结果"""
        
        try:
            article = {
                "title": result.get("title", ""),
                "url": result.get("url", ""),
                "summary": result.get("snippet", ""),
                "published_date": result.get("published_date", ""),
                "author": "",
                "source": result.get("source", "Search"),
                "source_type": "search",
                "priority": query_info.get("priority", 3),
                "keywords": [query_info["query"]],
                "content": ""
            }
            
            return article
            
        except Exception as e:
            logger.warning(f"解析搜索结果失败: {str(e)}")
            return None
    
    def _deduplicate_articles(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重文章"""
        
        seen_urls = set()
        seen_titles = set()
        unique_articles = []
        
        for article in articles:
            url = article.get("url", "")
            title = article.get("title", "").lower().strip()
            
            # URL去重
            if url and url in seen_urls:
                continue
            
            # 标题相似性去重
            if title and any(self._is_similar_title(title, seen_title) for seen_title in seen_titles):
                continue
            
            seen_urls.add(url)
            seen_titles.add(title)
            unique_articles.append(article)
        
        return unique_articles
    
    def _is_similar_title(self, title1: str, title2: str, threshold: float = 0.8) -> bool:
        """检查标题相似性"""
        
        # 简单的相似性检查
        words1 = set(title1.split())
        words2 = set(title2.split())
        
        if not words1 or not words2:
            return False
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        similarity = intersection / union if union > 0 else 0
        return similarity >= threshold
    
    def _filter_by_relevance(self, articles: List[Dict[str, Any]], topic: Optional[str]) -> List[Dict[str, Any]]:
        """按相关性过滤文章"""
        
        if not topic:
            return articles
        
        filtered = []
        topic_lower = topic.lower()
        
        for article in articles:
            title = article.get("title", "").lower()
            summary = article.get("summary", "").lower()
            content = f"{title} {summary}"
            
            # 计算相关性分数
            relevance_score = 0
            
            # 标题匹配
            if topic_lower in title:
                relevance_score += 3
            
            # 摘要匹配
            if topic_lower in summary:
                relevance_score += 2
            
            # 关键词匹配
            keywords = article.get("keywords", [])
            for keyword in keywords:
                if keyword.lower() in content:
                    relevance_score += 1
            
            # 设置相关性阈值
            if relevance_score >= 1:
                article["relevance_score"] = relevance_score
                filtered.append(article)
        
        return filtered
    
    def _sort_by_importance(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按重要性排序文章"""
        
        def importance_score(article):
            score = 0
            
            # 优先级权重
            score += article.get("priority", 1) * 10
            
            # 相关性权重
            score += article.get("relevance_score", 0) * 5
            
            # 时间权重（越新越重要）
            pub_date = article.get("published_date", "")
            if pub_date:
                try:
                    date_obj = self._parse_date(pub_date)
                    if date_obj:
                        days_ago = (datetime.now() - date_obj).days
                        if days_ago <= 1:
                            score += 15  # 今天的文章
                        elif days_ago <= 7:
                            score += 10  # 一周内的文章
                        elif days_ago <= 30:
                            score += 5   # 一月内的文章
                except:
                    pass
            
            # 源类型权重
            source_type = article.get("source_type", "")
            if source_type == "arxiv":
                score += 8  # 学术论文权重高
            elif source_type == "rss":
                score += 6  # RSS源权重中等
            elif source_type == "search":
                score += 3  # 搜索结果权重低
            
            return score
        
        return sorted(articles, key=importance_score, reverse=True)
    
    def _is_recent_article(self, date_str: str, days_back: int = 30) -> bool:
        """检查文章是否为最近发布"""
        
        if not date_str:
            return True  # 如果没有日期，默认为最近
        
        try:
            date_obj = self._parse_date(date_str)
            if date_obj:
                cutoff_date = datetime.now() - timedelta(days=days_back)
                return date_obj >= cutoff_date
        except:
            pass
        
        return True
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """解析日期字符串"""
        
        if not date_str:
            return None
        
        # 常见日期格式
        formats = [
            "%Y-%m-%dT%H:%M:%SZ",
            "%Y-%m-%dT%H:%M:%S",
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d",
            "%a, %d %b %Y %H:%M:%S %Z",
            "%a, %d %b %Y %H:%M:%S"
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        return None
