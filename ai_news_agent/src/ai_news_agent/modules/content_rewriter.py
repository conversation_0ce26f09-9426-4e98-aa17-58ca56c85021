"""
内容改写模块 - 将技术性AI资讯改写为微信公众号文章
"""

from typing import Dict, Any, List, Optional
from ..tools.rewrite import RewriteTool
from ..tools.text_processing import TextProcessingTool
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ContentRewriter:
    """内容改写器"""
    
    def __init__(self):
        self.rewrite_tool = RewriteTool()
        self.text_processing_tool = TextProcessingTool()
        
        # 改写配置
        self.default_config = {
            "style": "wechat",
            "target_length": 1500,
            "tone": "casual",
            "add_interactions": True
        }
    
    def rewrite_article(
        self,
        title: str,
        content: str,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """改写文章"""
        
        # 合并配置
        rewrite_config = {**self.default_config, **(config or {})}
        
        logger.info(f"开始改写文章: {title}")
        
        try:
            # 1. 预处理内容
            processed_content = self._preprocess_content(content)
            
            # 2. 执行改写
            rewrite_result = self.rewrite_tool.forward(
                content=processed_content,
                title=title,
                **rewrite_config
            )
            
            import json
            result = json.loads(rewrite_result)
            
            if result.get("success"):
                logger.info("文章改写成功")
                return result
            else:
                logger.error(f"文章改写失败: {result.get('error')}")
                return {"success": False, "error": result.get("error")}
                
        except Exception as e:
            logger.error(f"文章改写异常: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _preprocess_content(self, content: str) -> str:
        """预处理内容"""
        
        try:
            # 使用文本处理工具清洗内容
            processing_result = self.text_processing_tool.forward(
                text=content,
                operation="clean"
            )
            
            import json
            result = json.loads(processing_result)
            
            if result.get("success") and "cleaned_text" in result:
                return result["cleaned_text"]
            else:
                return content
                
        except Exception as e:
            logger.warning(f"内容预处理失败: {str(e)}")
            return content


class BatchRewriter:
    """批量改写器"""
    
    def __init__(self):
        self.rewriter = ContentRewriter()
    
    def rewrite_multiple_articles(
        self,
        articles: List[Dict[str, Any]],
        config: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """批量改写文章"""
        
        logger.info(f"开始批量改写 {len(articles)} 篇文章")
        
        results = []
        
        for i, article in enumerate(articles):
            try:
                title = article.get("title", "")
                content = article.get("content", "") or article.get("summary", "")
                
                if not title or not content:
                    logger.warning(f"文章 {i+1} 缺少标题或内容，跳过")
                    continue
                
                # 改写文章
                rewrite_result = self.rewriter.rewrite_article(title, content, config)
                
                # 合并原始信息
                result = {
                    **article,
                    "rewrite_result": rewrite_result,
                    "rewritten": rewrite_result.get("success", False)
                }
                
                results.append(result)
                
                logger.info(f"完成文章 {i+1}/{len(articles)}")
                
            except Exception as e:
                logger.error(f"文章 {i+1} 改写失败: {str(e)}")
                results.append({
                    **article,
                    "rewrite_result": {"success": False, "error": str(e)},
                    "rewritten": False
                })
        
        logger.info(f"批量改写完成，成功 {sum(1 for r in results if r.get('rewritten'))} 篇")
        return results
