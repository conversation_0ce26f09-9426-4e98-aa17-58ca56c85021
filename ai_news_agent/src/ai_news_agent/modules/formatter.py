"""
格式化模块 - 文章格式化输出
"""

from typing import Dict, Any, List, Optional
from ..tools.format import FormatTool
from ..utils.logger import get_logger

logger = get_logger(__name__)


class Formatter:
    """格式化器"""
    
    def __init__(self):
        self.format_tool = FormatTool()
        
        # 默认格式化配置
        self.default_config = {
            "format_type": "both",
            "add_images": True,
            "add_seo": True,
            "author": "AI News Agent"
        }
    
    def format_article(
        self,
        title: str,
        content: str,
        config: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """格式化文章"""
        
        # 合并配置
        format_config = {**self.default_config, **(config or {})}
        
        logger.info(f"开始格式化文章: {title}")
        
        try:
            # 执行格式化
            format_result = self.format_tool.forward(
                title=title,
                content=content,
                tags=tags or [],
                **format_config
            )
            
            import json
            result = json.loads(format_result)
            
            if result.get("success"):
                logger.info("文章格式化成功")
                return result
            else:
                logger.error(f"文章格式化失败: {result.get('error')}")
                return {"success": False, "error": result.get("error")}
                
        except Exception as e:
            logger.error(f"文章格式化异常: {str(e)}")
            return {"success": False, "error": str(e)}


class BatchFormatter:
    """批量格式化器"""
    
    def __init__(self):
        self.formatter = Formatter()
    
    def format_multiple_articles(
        self,
        articles: List[Dict[str, Any]],
        config: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """批量格式化文章"""
        
        logger.info(f"开始批量格式化 {len(articles)} 篇文章")
        
        results = []
        
        for i, article in enumerate(articles):
            try:
                # 获取改写后的内容
                rewrite_result = article.get("rewrite_result", {})
                
                if rewrite_result.get("success"):
                    title = rewrite_result.get("new_title", article.get("title", ""))
                    content = rewrite_result.get("rewritten_content", "")
                else:
                    title = article.get("title", "")
                    content = article.get("content", "") or article.get("summary", "")
                
                if not title or not content:
                    logger.warning(f"文章 {i+1} 缺少标题或内容，跳过")
                    continue
                
                # 生成标签
                tags = self._generate_tags(article)
                
                # 格式化文章
                format_result = self.formatter.format_article(title, content, config, tags)
                
                # 合并结果
                result = {
                    **article,
                    "format_result": format_result,
                    "formatted": format_result.get("success", False)
                }
                
                results.append(result)
                
                logger.info(f"完成文章 {i+1}/{len(articles)}")
                
            except Exception as e:
                logger.error(f"文章 {i+1} 格式化失败: {str(e)}")
                results.append({
                    **article,
                    "format_result": {"success": False, "error": str(e)},
                    "formatted": False
                })
        
        logger.info(f"批量格式化完成，成功 {sum(1 for r in results if r.get('formatted'))} 篇")
        return results
    
    def _generate_tags(self, article: Dict[str, Any]) -> List[str]:
        """生成文章标签"""
        
        tags = []
        
        # 从关键词生成标签
        keywords = article.get("keywords", [])
        if keywords:
            tags.extend(keywords[:5])
        
        # 从源添加标签
        source = article.get("source", "")
        if source:
            tags.append(source)
        
        # 添加默认AI标签
        tags.extend(["AI", "人工智能", "科技资讯"])
        
        # 去重并限制数量
        unique_tags = list(dict.fromkeys(tags))  # 保持顺序的去重
        return unique_tags[:8]
