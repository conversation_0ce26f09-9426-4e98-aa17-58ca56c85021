"""
命令行接口模块
"""

import asyncio
import click
import json
import os
from datetime import datetime
from typing import Optional

from .agent import AINewsAgent
from .config import ConfigManager
from .utils.logger import setup_logging, get_logger

logger = get_logger(__name__)


@click.group()
@click.option('--config', '-c', help='配置文件路径')
@click.option('--debug', '-d', is_flag=True, help='启用调试模式')
@click.option('--log-level', default='INFO', help='日志级别')
@click.pass_context
def cli(ctx, config, debug, log_level):
    """AI News Agent - AI资讯微信公众号智能体"""
    
    # 设置日志
    setup_logging(level=log_level)
    
    # 初始化配置管理器
    config_manager = ConfigManager(config)
    
    # 更新调试模式
    if debug:
        config_manager.config.debug = True
        config_manager.config.log_level = 'DEBUG'
        setup_logging(level='DEBUG')
    
    # 保存到上下文
    ctx.ensure_object(dict)
    ctx.obj['config_manager'] = config_manager
    ctx.obj['debug'] = debug


@cli.command()
@click.option('--topic', '-t', help='指定主题')
@click.option('--count', '-n', default=5, help='文章数量')
@click.option('--format', '-f', default='both', help='输出格式: markdown, wechat, both')
@click.option('--output', '-o', help='输出目录')
@click.pass_context
def run(ctx, topic, count, format, output):
    """运行AI资讯处理"""
    
    config_manager = ctx.obj['config_manager']
    
    try:
        # 创建智能体
        agent = AINewsAgent(config=config_manager.config.data_sources)
        
        # 处理资讯
        if topic:
            click.echo(f"🔍 正在处理主题: {topic}")
            articles = asyncio.run(agent.process_news_topic(topic, count, format))
        else:
            click.echo("📰 正在获取最新AI资讯...")
            articles = asyncio.run(agent.process_latest_news(count, format))
        
        if not articles:
            click.echo("❌ 未找到相关资讯")
            return
        
        # 保存结果
        output_dir = output or config_manager.config.output.output_path
        saved_files = save_articles(articles, output_dir, format)
        
        # 显示结果
        click.echo(f"✅ 处理完成！生成 {len(articles)} 篇文章")
        click.echo(f"📁 文件保存在: {output_dir}")
        
        for file_path in saved_files:
            click.echo(f"   - {file_path}")
            
    except Exception as e:
        logger.error(f"运行失败: {str(e)}")
        click.echo(f"❌ 运行失败: {str(e)}")


@cli.command()
@click.argument('url')
@click.option('--format', '-f', default='both', help='输出格式')
@click.option('--output', '-o', help='输出目录')
@click.pass_context
def process_url(ctx, url, format, output):
    """处理单个URL"""
    
    config_manager = ctx.obj['config_manager']
    
    try:
        # 创建智能体
        agent = AINewsAgent(config=config_manager.config.data_sources)
        
        click.echo(f"🔗 正在处理URL: {url}")
        
        # 处理URL
        article = asyncio.run(agent.process_single_url(url, format))
        
        if not article:
            click.echo("❌ URL处理失败")
            return
        
        # 保存结果
        output_dir = output or config_manager.config.output.output_path
        saved_files = save_articles([article], output_dir, format)
        
        # 显示结果
        click.echo("✅ URL处理完成！")
        click.echo(f"📁 文件保存在: {output_dir}")
        
        for file_path in saved_files:
            click.echo(f"   - {file_path}")
            
    except Exception as e:
        logger.error(f"URL处理失败: {str(e)}")
        click.echo(f"❌ URL处理失败: {str(e)}")


@cli.command()
@click.pass_context
def config_show(ctx):
    """显示当前配置"""
    
    config_manager = ctx.obj['config_manager']
    summary = config_manager.get_config_summary()
    
    click.echo("📋 当前配置:")
    for key, value in summary.items():
        click.echo(f"   {key}: {value}")


def save_articles(articles, output_dir, format_type):
    """保存文章到文件"""
    
    os.makedirs(output_dir, exist_ok=True)
    saved_files = []
    
    for i, article in enumerate(articles):
        try:
            # 生成文件名
            title = article.get('title', f'article_{i+1}')
            # 清理文件名
            safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).strip()[:50]
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存不同格式
            if format_type in ['markdown', 'both']:
                format_result = article.get('format_result', {})
                if 'markdown' in format_result:
                    md_filename = f"{timestamp}_{safe_title}.md"
                    md_path = os.path.join(output_dir, md_filename)
                    
                    with open(md_path, 'w', encoding='utf-8') as f:
                        f.write(format_result['markdown'])
                    
                    saved_files.append(md_path)
            
            if format_type in ['wechat', 'both']:
                format_result = article.get('format_result', {})
                if 'wechat' in format_result:
                    html_filename = f"{timestamp}_{safe_title}_wechat.html"
                    html_path = os.path.join(output_dir, html_filename)
                    
                    with open(html_path, 'w', encoding='utf-8') as f:
                        f.write(format_result['wechat'])
                    
                    saved_files.append(html_path)
            
            # 保存原始数据
            json_filename = f"{timestamp}_{safe_title}_data.json"
            json_path = os.path.join(output_dir, json_filename)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(article, f, ensure_ascii=False, indent=2)
            
            saved_files.append(json_path)
            
        except Exception as e:
            logger.warning(f"保存文章 {i+1} 失败: {str(e)}")
    
    return saved_files


def main():
    """主入口函数"""
    cli()


if __name__ == '__main__':
    main()
