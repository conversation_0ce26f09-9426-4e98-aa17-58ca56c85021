"""
时间处理工具函数
"""

from datetime import datetime
from typing import Optional


def get_current_time() -> datetime:
    """获取当前时间"""
    return datetime.now()


def format_time(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化时间"""
    return dt.strftime(format_str)


def parse_time(time_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
    """解析时间字符串"""
    try:
        return datetime.strptime(time_str, format_str)
    except ValueError:
        return None
