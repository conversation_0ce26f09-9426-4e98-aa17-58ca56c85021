"""
文本处理工具函数
"""

import re
from typing import List, Optional


def clean_text(text: str) -> str:
    """清理文本"""
    if not text:
        return ""
    
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 移除多余的换行
    text = re.sub(r'\n\s*\n', '\n\n', text)
    
    return text.strip()


def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
    """提取关键词"""
    if not text:
        return []
    
    # 简单的关键词提取
    words = re.findall(r'\b\w+\b', text.lower())
    
    # 过滤停用词和短词
    stopwords = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
    filtered_words = [word for word in words if len(word) > 2 and word not in stopwords]
    
    # 统计词频
    from collections import Counter
    word_freq = Counter(filtered_words)
    
    # 返回最常见的词
    return [word for word, _ in word_freq.most_common(max_keywords)]


def truncate_text(text: str, max_length: int = 100) -> str:
    """截断文本"""
    if not text or len(text) <= max_length:
        return text
    
    # 在单词边界截断
    truncated = text[:max_length]
    last_space = truncated.rfind(' ')
    
    if last_space > max_length * 0.8:  # 如果最后一个空格位置合理
        truncated = truncated[:last_space]
    
    return truncated + '...'
