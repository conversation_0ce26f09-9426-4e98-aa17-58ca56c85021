"""
AI News Agent - 主智能体类
"""

import asyncio
import json
from typing import Dict, Any, List, Optional
from smolagents import CodeAgent, InferenceClientModel

from .tools import (
    WebSearchTool, ContentFetchTool, TextProcessingTool, 
    RewriteTool, FormatTool
)
from .modules import NewsFetcher, ContentRewriter, Formatter
from .config import DataSourceConfig
from .utils.logger import get_logger

logger = get_logger(__name__)


class AINewsAgent:
    """
    AI资讯微信公众号智能体
    
    主要功能：
    1. 获取最新AI资讯
    2. 改写为微信公众号文章
    3. 格式化输出
    4. 批量处理
    """
    
    def __init__(
        self,
        model: Optional[Any] = None,
        config: Optional[DataSourceConfig] = None
    ):
        # 初始化模型
        if model is None:
            model = InferenceClientModel()
        
        # 初始化工具
        self.tools = [
            WebSearchTool(),
            ContentFetchTool(),
            TextProcessingTool(),
            RewriteTool(),
            FormatTool()
        ]
        
        # 创建CodeAgent
        self.agent = CodeAgent(
            tools=self.tools,
            model=model,
            stream_outputs=True
        )
        
        # 初始化模块
        self.news_fetcher = NewsFetcher(config)
        self.content_rewriter = ContentRewriter()
        self.formatter = Formatter()
        
        logger.info("AI News Agent 初始化完成")
    
    async def process_news_topic(
        self,
        topic: str,
        max_articles: int = 5,
        output_format: str = "both"
    ) -> List[Dict[str, Any]]:
        """处理指定主题的AI资讯"""
        
        logger.info(f"开始处理主题: {topic}")
        
        try:
            # 1. 获取资讯
            articles = await self.news_fetcher.fetch_latest_news(
                topic=topic,
                max_articles=max_articles * 2,  # 获取更多以便筛选
                time_filter="recent"
            )
            
            if not articles:
                logger.warning(f"未找到关于 {topic} 的相关资讯")
                return []
            
            # 2. 获取完整内容
            enriched_articles = await self._enrich_articles_content(articles[:max_articles])
            
            # 3. 改写内容
            rewritten_articles = self._rewrite_articles(enriched_articles)
            
            # 4. 格式化输出
            formatted_articles = self._format_articles(rewritten_articles, output_format)
            
            logger.info(f"主题 {topic} 处理完成，生成 {len(formatted_articles)} 篇文章")
            return formatted_articles
            
        except Exception as e:
            logger.error(f"处理主题 {topic} 失败: {str(e)}")
            return []
    
    async def process_latest_news(
        self,
        max_articles: int = 10,
        output_format: str = "both"
    ) -> List[Dict[str, Any]]:
        """处理最新AI资讯"""
        
        logger.info("开始处理最新AI资讯")
        
        try:
            # 1. 获取最新资讯
            articles = await self.news_fetcher.fetch_latest_news(
                topic=None,
                max_articles=max_articles * 2,
                time_filter="latest"
            )
            
            if not articles:
                logger.warning("未找到最新AI资讯")
                return []
            
            # 2. 获取完整内容
            enriched_articles = await self._enrich_articles_content(articles[:max_articles])
            
            # 3. 改写内容
            rewritten_articles = self._rewrite_articles(enriched_articles)
            
            # 4. 格式化输出
            formatted_articles = self._format_articles(rewritten_articles, output_format)
            
            logger.info(f"最新资讯处理完成，生成 {len(formatted_articles)} 篇文章")
            return formatted_articles
            
        except Exception as e:
            logger.error(f"处理最新资讯失败: {str(e)}")
            return []
    
    async def process_single_url(
        self,
        url: str,
        output_format: str = "both"
    ) -> Optional[Dict[str, Any]]:
        """处理单个URL的文章"""
        
        logger.info(f"开始处理URL: {url}")
        
        try:
            # 1. 获取内容
            content_result = self.tools[1].forward(url=url)  # ContentFetchTool
            content_data = json.loads(content_result)
            
            if not content_data.get("success"):
                logger.error(f"获取URL内容失败: {content_data.get('error')}")
                return None
            
            # 2. 构建文章对象
            article = {
                "title": content_data.get("title", ""),
                "content": content_data.get("content", ""),
                "url": url,
                "author": content_data.get("author", ""),
                "published_date": content_data.get("published_date", ""),
                "source": "Manual URL",
                "source_type": "web"
            }
            
            # 3. 改写内容
            rewrite_result = self.content_rewriter.rewrite_article(
                article["title"],
                article["content"]
            )
            article["rewrite_result"] = rewrite_result
            article["rewritten"] = rewrite_result.get("success", False)
            
            # 4. 格式化输出
            if article["rewritten"]:
                format_result = self.formatter.format_article(
                    rewrite_result.get("new_title", article["title"]),
                    rewrite_result.get("rewritten_content", ""),
                    {"format_type": output_format}
                )
                article["format_result"] = format_result
                article["formatted"] = format_result.get("success", False)
            
            logger.info(f"URL处理完成: {url}")
            return article
            
        except Exception as e:
            logger.error(f"处理URL失败: {str(e)}")
            return None
    
    async def _enrich_articles_content(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """丰富文章内容"""
        
        enriched = []
        
        for article in articles:
            try:
                url = article.get("url", "")
                if not url:
                    enriched.append(article)
                    continue
                
                # 获取完整内容
                content_result = self.tools[1].forward(url=url)  # ContentFetchTool
                content_data = json.loads(content_result)
                
                if content_data.get("success"):
                    # 更新文章内容
                    article["content"] = content_data.get("content", article.get("content", ""))
                    article["word_count"] = content_data.get("word_count", 0)
                    article["images"] = content_data.get("images", [])
                
                enriched.append(article)
                
            except Exception as e:
                logger.warning(f"获取文章内容失败: {str(e)}")
                enriched.append(article)
        
        return enriched
    
    def _rewrite_articles(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """改写文章"""
        
        rewriter = ContentRewriter()
        
        for article in articles:
            try:
                title = article.get("title", "")
                content = article.get("content", "") or article.get("summary", "")
                
                if title and content:
                    rewrite_result = rewriter.rewrite_article(title, content)
                    article["rewrite_result"] = rewrite_result
                    article["rewritten"] = rewrite_result.get("success", False)
                else:
                    article["rewritten"] = False
                    
            except Exception as e:
                logger.warning(f"文章改写失败: {str(e)}")
                article["rewritten"] = False
        
        return articles
    
    def _format_articles(
        self, 
        articles: List[Dict[str, Any]], 
        output_format: str
    ) -> List[Dict[str, Any]]:
        """格式化文章"""
        
        formatter = Formatter()
        
        for article in articles:
            try:
                if not article.get("rewritten"):
                    continue
                
                rewrite_result = article.get("rewrite_result", {})
                title = rewrite_result.get("new_title", article.get("title", ""))
                content = rewrite_result.get("rewritten_content", "")
                
                if title and content:
                    format_result = formatter.format_article(
                        title, 
                        content,
                        {"format_type": output_format}
                    )
                    article["format_result"] = format_result
                    article["formatted"] = format_result.get("success", False)
                else:
                    article["formatted"] = False
                    
            except Exception as e:
                logger.warning(f"文章格式化失败: {str(e)}")
                article["formatted"] = False
        
        return articles
    
    def run(self, query: str) -> str:
        """运行智能体（兼容smolagents接口）"""
        
        try:
            # 解析查询
            if "最新" in query or "latest" in query.lower():
                # 获取最新资讯
                result = asyncio.run(self.process_latest_news(max_articles=5))
            elif any(topic in query for topic in ["GPT", "ChatGPT", "OpenAI", "AI", "人工智能"]):
                # 处理特定主题
                topic = self._extract_topic_from_query(query)
                result = asyncio.run(self.process_news_topic(topic, max_articles=3))
            else:
                # 默认处理
                result = asyncio.run(self.process_latest_news(max_articles=3))
            
            # 格式化返回结果
            return self._format_response(result)
            
        except Exception as e:
            logger.error(f"智能体运行失败: {str(e)}")
            return f"处理失败: {str(e)}"
    
    def _extract_topic_from_query(self, query: str) -> str:
        """从查询中提取主题"""
        
        # 简单的主题提取
        ai_topics = [
            "GPT", "ChatGPT", "OpenAI", "Google AI", "DeepMind",
            "机器学习", "深度学习", "人工智能", "AI", "自然语言处理"
        ]
        
        for topic in ai_topics:
            if topic in query:
                return topic
        
        return "AI"
    
    def _format_response(self, articles: List[Dict[str, Any]]) -> str:
        """格式化响应"""
        
        if not articles:
            return "未找到相关资讯。"
        
        response_parts = [f"找到 {len(articles)} 篇相关文章：\n"]
        
        for i, article in enumerate(articles, 1):
            title = article.get("title", "无标题")
            source = article.get("source", "未知来源")
            rewritten = article.get("rewritten", False)
            formatted = article.get("formatted", False)
            
            status = "✅ 已处理" if rewritten and formatted else "⚠️ 处理中"
            
            response_parts.append(f"{i}. {title}")
            response_parts.append(f"   来源: {source}")
            response_parts.append(f"   状态: {status}\n")
        
        return "\n".join(response_parts)
