# 安装指南

## 系统要求

- Python 3.8 或更高版本
- 稳定的网络连接
- 至少 2GB 可用内存

## 安装步骤

### 1. 克隆项目

```bash
git clone https://github.com/ai-news-agent/ai-news-agent.git
cd ai-news-agent
```

### 2. 创建虚拟环境

```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

### 3. 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 或者使用开发模式安装
pip install -e .

# 安装开发依赖（可选）
pip install -e ".[dev]"
```

### 4. 配置环境变量

创建 `.env` 文件：

```bash
# LLM API 密钥
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# 可选配置
AI_NEWS_AGENT_LOG_LEVEL=INFO
AI_NEWS_AGENT_OUTPUT_PATH=output
```

### 5. 验证安装

```bash
# 运行测试
python -m pytest tests/

# 运行示例
python examples/basic_usage.py

# 使用CLI
ai-news-agent --help
```

## 常见问题

### 依赖安装失败

如果遇到依赖安装问题，尝试：

```bash
# 升级pip
pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 权限问题

在某些系统上可能需要管理员权限：

```bash
# Windows (以管理员身份运行)
# Linux/Mac
sudo pip install -r requirements.txt
```

### 网络问题

如果网络连接不稳定，可以：

1. 使用代理
2. 下载离线包
3. 使用本地镜像源

## 下一步

安装完成后，请查看：

- [配置说明](configuration.md)
- [快速开始](../README.md#快速开始)
- [API文档](api.md)
