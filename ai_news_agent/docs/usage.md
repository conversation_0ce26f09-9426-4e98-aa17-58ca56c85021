# 使用指南

## 命令行使用

### 基本命令

```bash
# 获取最新AI资讯
ai-news-agent run

# 处理特定主题
ai-news-agent run --topic "GPT-4" --count 5

# 处理单个URL
ai-news-agent process-url https://example.com/ai-news

# 显示配置
ai-news-agent config-show

# 列出数据源
ai-news-agent sources-list
```

### 参数说明

- `--topic, -t`: 指定主题关键词
- `--count, -n`: 文章数量（默认5）
- `--format, -f`: 输出格式（markdown/wechat/both）
- `--output, -o`: 输出目录
- `--config, -c`: 配置文件路径
- `--debug, -d`: 启用调试模式

## Python API 使用

### 基本用法

```python
import asyncio
from ai_news_agent import AINewsAgent

# 创建智能体
agent = AINewsAgent()

# 获取最新资讯
articles = asyncio.run(agent.process_latest_news(max_articles=5))

# 处理特定主题
gpt_articles = asyncio.run(agent.process_news_topic("GPT", max_articles=3))

# 处理单个URL
article = asyncio.run(agent.process_single_url("https://example.com/news"))
```

### 高级配置

```python
from ai_news_agent import AINewsAgent
from ai_news_agent.config import ConfigManager
from smolagents import InferenceClientModel

# 自定义配置
config_manager = ConfigManager("custom_config.yaml")

# 自定义模型
model = InferenceClientModel(model_id="gpt-4")

# 创建智能体
agent = AINewsAgent(
    model=model,
    config=config_manager.config.data_sources
)
```

## 配置文件

### 创建配置文件

```yaml
# config.yaml
llm:
  provider: "openai"
  model: "gpt-4"
  temperature: 0.7

output:
  default_format: "both"
  output_path: "my_output"
  wechat_style: "enthusiastic"

data_sources:
  sources:
    - name: "Custom Source"
      url: "https://example.com/feed"
      source_type: "rss"
      enabled: true
      priority: 5
```

### 使用配置文件

```bash
ai-news-agent run --config config.yaml
```

## 输出格式

### Markdown 格式

生成标准的Markdown文件，包含：
- 文章标题和元数据
- 格式化的正文内容
- 图片占位符
- SEO标签

### 微信公众号格式

生成HTML格式，包含：
- 微信公众号样式
- 互动元素
- 图片建议
- 发布建议

## 批量处理

```python
# 批量处理多个主题
topics = ["GPT", "机器学习", "深度学习"]
all_articles = []

for topic in topics:
    articles = await agent.process_news_topic(topic, max_articles=2)
    all_articles.extend(articles)
```

## 错误处理

```python
try:
    articles = await agent.process_latest_news()
except Exception as e:
    print(f"处理失败: {e}")
    # 处理错误
```

## 性能优化

1. **并发处理**: 使用 asyncio 进行并发请求
2. **缓存**: 启用内容缓存减少重复请求
3. **限流**: 设置合理的请求频率
4. **过滤**: 使用关键词过滤减少无关内容

## 最佳实践

1. **定期更新**: 建议每天运行1-2次
2. **主题聚焦**: 使用具体的主题关键词
3. **质量检查**: 人工审核生成的内容
4. **备份配置**: 保存重要的配置文件
5. **监控日志**: 定期检查错误日志
