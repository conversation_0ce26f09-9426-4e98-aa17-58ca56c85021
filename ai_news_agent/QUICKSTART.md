# 🚀 快速开始指南

## 5分钟上手 AI News Agent

### 1. 安装

```bash
# 克隆项目
git clone https://github.com/ai-news-agent/ai-news-agent.git
cd ai_news_agent

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置API密钥

```bash
# 设置环境变量
export OPENAI_API_KEY="your-openai-api-key"

# 或者创建 .env 文件
echo "OPENAI_API_KEY=your-openai-api-key" > .env
```

### 3. 运行第一个示例

```bash
# 获取最新AI资讯
python -m ai_news_agent.cli run --count 3

# 或者运行示例脚本
python examples/basic_usage.py
```

### 4. 查看结果

生成的文章将保存在 `output/` 目录中：
- `*.md` - Markdown格式
- `*_wechat.html` - 微信公众号格式
- `*_data.json` - 原始数据

### 5. 自定义使用

```python
import asyncio
from ai_news_agent import AINewsAgent

# 创建智能体
agent = AINewsAgent()

# 处理特定主题
articles = asyncio.run(
    agent.process_news_topic("GPT-4", max_articles=5)
)

print(f"生成了 {len(articles)} 篇文章")
```

## 常用命令

```bash
# 处理特定主题
ai-news-agent run --topic "机器学习" --count 5

# 处理单个URL
ai-news-agent process-url https://example.com/ai-news

# 查看配置
ai-news-agent config-show

# 列出数据源
ai-news-agent sources-list
```

## 下一步

- 📖 阅读 [完整文档](README.md)
- ⚙️ 查看 [配置选项](docs/usage.md)
- 🔧 自定义 [数据源](config/default.yaml)
- 🧪 运行 [测试](tests/)

## 需要帮助？

- 查看 [常见问题](docs/faq.md)
- 提交 [Issue](https://github.com/ai-news-agent/ai-news-agent/issues)
- 加入讨论群
