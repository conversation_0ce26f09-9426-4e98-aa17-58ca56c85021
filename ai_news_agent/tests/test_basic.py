"""
基础测试模块
"""

import unittest
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from ai_news_agent.config import DataSourceConfig, LLMConfig, OutputConfig
from ai_news_agent.tools import WebSearchTool, TextProcessingTool
from ai_news_agent.utils.text_utils import clean_text, extract_keywords, truncate_text


class TestConfig(unittest.TestCase):
    """测试配置模块"""
    
    def test_data_source_config(self):
        """测试数据源配置"""
        config = DataSourceConfig()
        sources = config.get_enabled_sources()
        self.assertGreater(len(sources), 0)
        
        # 测试按优先级获取
        high_priority = config.get_sources_by_priority(min_priority=4)
        self.assertGreater(len(high_priority), 0)
    
    def test_llm_config(self):
        """测试LLM配置"""
        config = LLMConfig()
        self.assertEqual(config.provider, "openai")
        self.assertEqual(config.model, "gpt-3.5-turbo")
        self.assertEqual(config.temperature, 0.7)
    
    def test_output_config(self):
        """测试输出配置"""
        config = OutputConfig()
        self.assertEqual(config.default_format, "both")
        self.assertEqual(config.output_path, "output")
        self.assertTrue(config.add_images)


class TestTools(unittest.TestCase):
    """测试工具模块"""
    
    def test_web_search_tool(self):
        """测试网络搜索工具"""
        tool = WebSearchTool()
        self.assertEqual(tool.name, "web_search")
        self.assertIn("query", tool.inputs)
    
    def test_text_processing_tool(self):
        """测试文本处理工具"""
        tool = TextProcessingTool()
        self.assertEqual(tool.name, "text_processing")
        self.assertIn("text", tool.inputs)


class TestUtils(unittest.TestCase):
    """测试工具函数"""
    
    def test_clean_text(self):
        """测试文本清理"""
        dirty_text = "<p>Hello   world</p>\n\n\nTest"
        clean = clean_text(dirty_text)
        self.assertEqual(clean, "Hello world\n\nTest")
    
    def test_extract_keywords(self):
        """测试关键词提取"""
        text = "artificial intelligence machine learning deep learning AI"
        keywords = extract_keywords(text, max_keywords=3)
        self.assertLessEqual(len(keywords), 3)
        self.assertIn("artificial", keywords)
    
    def test_truncate_text(self):
        """测试文本截断"""
        long_text = "This is a very long text that should be truncated"
        truncated = truncate_text(long_text, max_length=20)
        self.assertLessEqual(len(truncated), 23)  # 包括 "..."
        self.assertTrue(truncated.endswith("..."))


if __name__ == "__main__":
    unittest.main()
