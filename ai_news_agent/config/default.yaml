# AI News Agent 默认配置文件

# 应用配置
app_name: "AI News Agent"
version: "0.1.0"
debug: false
log_level: "INFO"
max_articles_per_run: 10
default_time_filter: "recent"
enable_batch_processing: true

# LLM配置
llm:
  provider: "openai"  # openai, anthropic, huggingface, local
  model: "gpt-3.5-turbo"
  api_key: null  # 从环境变量获取
  api_base: null
  temperature: 0.7
  max_tokens: 2048
  timeout: 30
  huggingface_model_id: null
  local_model_path: null

# 数据源配置
data_sources:
  sources:
    - name: "arXiv AI"
      url: "http://export.arxiv.org/api/query"
      source_type: "arxiv"
      enabled: true
      priority: 4
      update_frequency: 120
      max_articles: 15
      keywords: ["artificial intelligence", "machine learning", "deep learning"]
      
    - name: "TechCrunch AI"
      url: "https://techcrunch.com/category/artificial-intelligence/feed/"
      source_type: "rss"
      enabled: true
      priority: 5
      update_frequency: 30
      max_articles: 10
      keywords: ["AI", "artificial intelligence", "machine learning"]
      
    - name: "OpenAI Blog"
      url: "https://openai.com/blog/rss.xml"
      source_type: "rss"
      enabled: true
      priority: 5
      update_frequency: 180
      max_articles: 5
      keywords: ["GPT", "ChatGPT", "AI research"]

# 输出配置
output:
  default_format: "both"  # markdown, wechat, both
  output_path: "output"
  save_original: true
  save_rewritten: true
  save_formatted: true
  filename_template: "{date}_{title}_{format}"
  date_format: "%Y%m%d_%H%M%S"
  add_images: true
  add_seo: true
  add_metadata: true
  wechat_style: "casual"  # professional, casual, enthusiastic
  wechat_add_interactions: true
  wechat_target_length: 1500
