"""
AI News Agent 完整演示脚本
"""

import asyncio
import os
import sys
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from ai_news_agent import AINewsAgent
from ai_news_agent.config import ConfigManager
from ai_news_agent.tools import WebSearchTool, TextProcessingTool


def print_banner():
    """打印横幅"""
    print("🤖 AI News Agent 完整演示")
    print("=" * 60)
    print("基于smolagents框架的AI资讯微信公众号智能体")
    print("功能：获取AI资讯 → 智能改写 → 格式化输出")
    print("=" * 60)


def demo_tools():
    """演示工具功能"""
    print("\n🛠️ 工具功能演示")
    print("-" * 30)
    
    # 1. 演示搜索工具
    print("1️⃣ 网络搜索工具演示")
    search_tool = WebSearchTool()
    
    try:
        result = search_tool.forward(
            query="ChatGPT 最新更新",
            source="duckduckgo",
            max_results=3
        )
        
        search_data = json.loads(result)
        if isinstance(search_data, list) and search_data:
            print(f"   ✅ 找到 {len(search_data)} 个搜索结果")
            for i, item in enumerate(search_data[:2], 1):
                print(f"   {i}. {item.get('title', '无标题')[:50]}...")
        else:
            print("   ⚠️ 搜索结果为空或格式异常")
    except Exception as e:
        print(f"   ❌ 搜索失败: {e}")
    
    # 2. 演示文本处理工具
    print("\n2️⃣ 文本处理工具演示")
    text_tool = TextProcessingTool()
    
    sample_text = """
    OpenAI发布了ChatGPT的最新版本，这个版本在自然语言处理方面有了显著的改进。
    新版本支持更长的对话上下文，并且在代码生成和数学推理方面表现更好。
    这项技术的进步将对人工智能行业产生深远的影响。
    """
    
    try:
        result = text_tool.forward(
            text=sample_text,
            operation="keywords",
            max_keywords=5
        )
        
        text_data = json.loads(result)
        if text_data.get("success") and "keywords" in text_data:
            keywords = text_data["keywords"]
            print(f"   ✅ 提取到 {len(keywords)} 个关键词:")
            for kw in keywords[:3]:
                print(f"   - {kw['word']} (权重: {kw['weight']})")
        else:
            print("   ⚠️ 关键词提取失败")
    except Exception as e:
        print(f"   ❌ 文本处理失败: {e}")


async def demo_agent():
    """演示智能体功能"""
    print("\n🤖 智能体功能演示")
    print("-" * 30)
    
    try:
        # 创建智能体
        print("1️⃣ 初始化智能体...")
        config_manager = ConfigManager()
        agent = AINewsAgent(config=config_manager.config.data_sources)
        print("   ✅ 智能体创建成功")
        
        # 演示最新资讯获取
        print("\n2️⃣ 获取最新AI资讯...")
        articles = await agent.process_latest_news(max_articles=2, output_format="markdown")
        
        if articles:
            print(f"   ✅ 成功获取 {len(articles)} 篇文章")
            
            for i, article in enumerate(articles, 1):
                print(f"\n   📄 文章 {i}:")
                print(f"   标题: {article.get('title', '无标题')[:60]}...")
                print(f"   来源: {article.get('source', '未知')}")
                print(f"   改写: {'✅' if article.get('rewritten') else '❌'}")
                print(f"   格式化: {'✅' if article.get('formatted') else '❌'}")
                
                # 显示改写结果
                if article.get('rewritten'):
                    rewrite_result = article.get('rewrite_result', {})
                    new_title = rewrite_result.get('new_title', '')
                    if new_title:
                        print(f"   新标题: {new_title[:60]}...")
        else:
            print("   ⚠️ 未获取到文章，可能是网络问题或API限制")
        
        # 演示特定主题处理
        print("\n3️⃣ 处理特定主题: 'AI'...")
        topic_articles = await agent.process_news_topic("AI", max_articles=1, output_format="both")
        
        if topic_articles:
            print(f"   ✅ 成功处理 {len(topic_articles)} 篇AI相关文章")
            
            article = topic_articles[0]
            format_result = article.get('format_result', {})
            
            if 'markdown' in format_result:
                print("   📝 Markdown格式已生成")
            if 'wechat' in format_result:
                print("   📱 微信公众号格式已生成")
        else:
            print("   ⚠️ 未找到AI相关文章")
        
        return articles + topic_articles if articles and topic_articles else []
        
    except Exception as e:
        print(f"   ❌ 智能体演示失败: {e}")
        return []


def demo_config():
    """演示配置功能"""
    print("\n⚙️ 配置功能演示")
    print("-" * 30)
    
    try:
        config_manager = ConfigManager()
        config = config_manager.config
        
        print("1️⃣ 当前配置:")
        print(f"   应用名称: {config.app_name}")
        print(f"   版本: {config.version}")
        print(f"   LLM提供商: {config.llm.provider}")
        print(f"   LLM模型: {config.llm.model}")
        print(f"   输出格式: {config.output.default_format}")
        print(f"   输出路径: {config.output.output_path}")
        
        print("\n2️⃣ 数据源配置:")
        sources = config.data_sources.get_enabled_sources()
        print(f"   启用的数据源: {len(sources)} 个")
        
        for source in sources[:3]:
            print(f"   - {source.name} (优先级: {source.priority})")
        
        if len(sources) > 3:
            print(f"   ... 还有 {len(sources) - 3} 个数据源")
        
    except Exception as e:
        print(f"   ❌ 配置演示失败: {e}")


def save_demo_results(articles):
    """保存演示结果"""
    if not articles:
        return
    
    print("\n💾 保存演示结果")
    print("-" * 30)
    
    try:
        output_dir = "demo_output"
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for i, article in enumerate(articles, 1):
            # 保存原始数据
            json_file = f"{output_dir}/demo_article_{i}_{timestamp}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(article, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 已保存: {json_file}")
            
            # 保存格式化结果
            format_result = article.get('format_result', {})
            
            if 'markdown' in format_result:
                md_file = f"{output_dir}/demo_article_{i}_{timestamp}.md"
                with open(md_file, 'w', encoding='utf-8') as f:
                    f.write(format_result['markdown'])
                print(f"   ✅ 已保存: {md_file}")
            
            if 'wechat' in format_result:
                html_file = f"{output_dir}/demo_article_{i}_{timestamp}_wechat.html"
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(format_result['wechat'])
                print(f"   ✅ 已保存: {html_file}")
        
        print(f"\n📁 所有文件已保存到: {output_dir}/")
        
    except Exception as e:
        print(f"   ❌ 保存失败: {e}")


async def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    print("\n🔍 环境检查")
    print("-" * 30)
    
    # 检查API密钥
    openai_key = os.getenv('OPENAI_API_KEY')
    anthropic_key = os.getenv('ANTHROPIC_API_KEY')
    
    if not openai_key and not anthropic_key:
        print("⚠️ 未检测到API密钥，某些功能可能无法正常工作")
        print("   请设置 OPENAI_API_KEY 或 ANTHROPIC_API_KEY 环境变量")
    else:
        print("✅ API密钥已配置")
    
    # 开始演示
    try:
        # 1. 工具演示
        demo_tools()
        
        # 2. 配置演示
        demo_config()
        
        # 3. 智能体演示
        articles = await demo_agent()
        
        # 4. 保存结果
        save_demo_results(articles)
        
        # 总结
        print("\n🎉 演示完成！")
        print("-" * 30)
        print("✅ 工具功能正常")
        print("✅ 配置加载成功")
        print("✅ 智能体运行正常")
        print("\n📚 更多使用方法:")
        print("- 查看 README.md")
        print("- 运行 python -m ai_news_agent.cli --help")
        print("- 查看 examples/ 目录下的其他示例")
        
    except KeyboardInterrupt:
        print("\n⚠️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
