"""
AI News Agent 基本使用示例
"""

import asyncio
import os
import sys

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from ai_news_agent import AINewsAgent
from ai_news_agent.config import ConfigManager


async def main():
    """主函数"""
    
    print("🤖 AI News Agent 示例")
    print("=" * 50)
    
    try:
        # 1. 初始化配置
        print("📋 初始化配置...")
        config_manager = ConfigManager()
        
        # 2. 创建智能体
        print("🚀 创建智能体...")
        agent = AINewsAgent(config=config_manager.config.data_sources)
        
        # 3. 获取最新AI资讯
        print("📰 获取最新AI资讯...")
        articles = await agent.process_latest_news(max_articles=3, output_format="markdown")
        
        if articles:
            print(f"✅ 成功获取 {len(articles)} 篇文章")
            
            for i, article in enumerate(articles, 1):
                print(f"\n📄 文章 {i}:")
                print(f"   标题: {article.get('title', '无标题')}")
                print(f"   来源: {article.get('source', '未知')}")
                print(f"   改写状态: {'✅' if article.get('rewritten') else '❌'}")
                print(f"   格式化状态: {'✅' if article.get('formatted') else '❌'}")
        else:
            print("❌ 未找到相关文章")
        
        # 4. 处理特定主题
        print("\n🔍 处理特定主题: GPT")
        gpt_articles = await agent.process_news_topic("GPT", max_articles=2, output_format="both")
        
        if gpt_articles:
            print(f"✅ 成功获取 {len(gpt_articles)} 篇GPT相关文章")
        else:
            print("❌ 未找到GPT相关文章")
        
        print("\n🎉 示例运行完成！")
        
    except Exception as e:
        print(f"❌ 运行失败: {str(e)}")


if __name__ == "__main__":
    # 设置环境变量（示例）
    # os.environ["OPENAI_API_KEY"] = "your-api-key-here"
    
    # 运行示例
    asyncio.run(main())
