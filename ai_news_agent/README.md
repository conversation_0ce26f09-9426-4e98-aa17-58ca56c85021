# AI News Agent

基于smolagents框架的AI资讯微信公众号智能体

## 🎯 项目简介

AI News Agent 是一个智能化的AI资讯处理系统，能够：

- 🔍 **自动获取最新AI资讯** - 从多个权威数据源收集最新AI新闻和研究
- ✍️ **智能内容改写** - 将技术性内容转化为适合微信公众号的易读文章
- 📝 **格式化输出** - 生成标准的Markdown格式和微信公众号发布格式
- 🤖 **全自动化流程** - 一键完成从资讯获取到文章生成的全流程

## 🚀 快速开始

### 安装

```bash
# 克隆项目
git clone https://github.com/ai-news-agent/ai-news-agent.git
cd ai-news-agent

# 安装依赖
pip install -r requirements.txt

# 或者使用开发模式安装
pip install -e .
```

### 基本使用

```bash
# 获取最新AI资讯并生成公众号文章
ai-news-agent run --topic "GPT-4" --count 3

# 使用配置文件
ai-news-agent run --config config/default.yaml

# 批量处理
ai-news-agent batch --input urls.txt --output articles/
```

### Python API

```python
from ai_news_agent import AINewsAgent
from smolagents import InferenceClientModel

# 创建智能体
model = InferenceClientModel(model_id="gpt-4")
agent = AINewsAgent(model=model)

# 处理单篇资讯
result = agent.process_news_url("https://example.com/ai-news")

# 批量处理
results = agent.process_multiple_topics(["GPT-4", "机器学习", "深度学习"])
```

## 📁 项目结构

```
ai_news_agent/
├── src/ai_news_agent/          # 主要源代码
│   ├── __init__.py
│   ├── agent.py                # 主智能体类
│   ├── tools/                  # 工具类
│   │   ├── web_search.py       # 网络搜索工具
│   │   ├── content_fetch.py    # 内容抓取工具
│   │   ├── text_processing.py  # 文本处理工具
│   │   ├── rewrite.py          # 内容改写工具
│   │   └── format.py           # 格式化工具
│   ├── modules/                # 功能模块
│   │   ├── news_fetcher.py     # 资讯获取模块
│   │   ├── content_rewriter.py # 内容改写模块
│   │   └── formatter.py        # 格式化模块
│   ├── config/                 # 配置管理
│   └── cli.py                  # 命令行接口
├── tests/                      # 测试文件
├── docs/                       # 文档
├── config/                     # 配置文件
├── examples/                   # 示例代码
└── logs/                       # 日志文件
```

## ⚙️ 配置

### 环境变量

```bash
# API密钥
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-anthropic-key"

# 可选配置
export AI_NEWS_AGENT_LOG_LEVEL="INFO"
export AI_NEWS_AGENT_CONFIG_PATH="config/custom.yaml"
```

### 配置文件

创建 `config/user_config.yaml`:

```yaml
# LLM配置
llm:
  provider: "openai"  # openai, anthropic, huggingface
  model: "gpt-4"
  temperature: 0.7

# 数据源配置
data_sources:
  - name: "arXiv"
    url: "https://arxiv.org/search/"
    enabled: true
  - name: "TechCrunch"
    url: "https://techcrunch.com/category/artificial-intelligence/"
    enabled: true

# 改写配置
rewrite:
  style: "wechat"  # wechat, blog, academic
  max_length: 2000
  add_images: true
  add_interactions: true

# 输出配置
output:
  format: "markdown"
  include_metadata: true
  save_path: "output/"
```

## 🛠️ 开发

### 开发环境设置

```bash
# 安装开发依赖
pip install -e ".[dev]"

# 安装pre-commit钩子
pre-commit install

# 运行测试
pytest

# 代码格式化
black src/ tests/
isort src/ tests/

# 类型检查
mypy src/
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest tests/unit/

# 运行集成测试
pytest tests/integration/

# 生成覆盖率报告
pytest --cov=ai_news_agent --cov-report=html
```

## 📚 文档

- [安装指南](docs/installation.md)
- [配置说明](docs/configuration.md)
- [API文档](docs/api.md)
- [开发指南](docs/development.md)
- [常见问题](docs/faq.md)

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](CONTRIBUTING.md) 了解详细信息。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [smolagents](https://github.com/huggingface/smolagents) - 强大的AI智能体框架
- [Hugging Face](https://huggingface.co/) - 提供优秀的AI模型和工具

## 📞 联系我们

- 项目主页: https://github.com/ai-news-agent/ai-news-agent
- 问题反馈: https://github.com/ai-news-agent/ai-news-agent/issues
- 邮箱: <EMAIL>
