[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-news-agent"
version = "0.1.0"
description = "基于smolagents框架的AI资讯微信公众号智能体"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "AI News Agent Team", email = "<EMAIL>"}
]
keywords = ["ai", "news", "wechat", "agent", "smolagents"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content :: News/Diary",
]
requires-python = ">=3.8"
dependencies = [
    "smolagents>=1.0.0",
    "requests>=2.28.0",
    "beautifulsoup4>=4.11.0",
    "lxml>=4.9.0",
    "pyyaml>=6.0",
    "click>=8.0.0",
    "rich>=12.0.0",
    "python-dateutil>=2.8.0",
    "feedparser>=6.0.0",
    "newspaper3k>=0.2.8",
    "openai>=1.0.0",
    "anthropic>=0.3.0",
    "transformers>=4.20.0",
    "torch>=1.12.0",
    "nltk>=3.7",
    "jieba>=0.42.1",
    "schedule>=1.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "flake8>=5.0.0",
    "mypy>=0.991",
    "pre-commit>=2.20.0",
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "myst-parser>=0.18.0",
]

[project.scripts]
ai-news-agent = "ai_news_agent.cli:main"

[project.urls]
Homepage = "https://github.com/ai-news-agent/ai-news-agent"
Documentation = "https://ai-news-agent.readthedocs.io"
Repository = "https://github.com/ai-news-agent/ai-news-agent"
"Bug Tracker" = "https://github.com/ai-news-agent/ai-news-agent/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
ai_news_agent = ["config/*.yaml", "templates/*.md"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["ai_news_agent"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=ai_news_agent",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "smolagents.*",
    "newspaper.*",
    "feedparser.*",
    "jieba.*",
]
ignore_missing_imports = true

[tool.coverage.run]
source = ["src/ai_news_agent"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
