#!/usr/bin/env python3
"""
AI News Agent 部署脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(cmd, check=True):
    """运行命令"""
    print(f"🔧 执行: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"❌ 命令执行失败: {cmd}")
        print(f"错误输出: {result.stderr}")
        sys.exit(1)
    
    return result


def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version}")


def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖...")
    
    # 升级pip
    run_command("python -m pip install --upgrade pip")
    
    # 安装依赖
    run_command("pip install -r requirements.txt")
    
    # 安装开发依赖（可选）
    result = run_command("pip install -e .", check=False)
    if result.returncode == 0:
        print("✅ 开发模式安装成功")
    else:
        print("⚠️ 开发模式安装失败，使用普通安装")
        run_command("pip install .")


def run_tests():
    """运行测试"""
    print("🧪 运行测试...")
    
    result = run_command("python tests/test_basic.py", check=False)
    
    if result.returncode == 0:
        print("✅ 所有测试通过")
    else:
        print("⚠️ 部分测试失败，但不影响部署")
        print(result.stdout)


def create_config():
    """创建配置文件"""
    print("⚙️ 创建配置文件...")
    
    config_dir = Path.home() / ".ai_news_agent"
    config_dir.mkdir(exist_ok=True)
    
    config_file = config_dir / "config.yaml"
    
    if not config_file.exists():
        # 复制默认配置
        default_config = Path("config/default.yaml")
        if default_config.exists():
            shutil.copy(default_config, config_file)
            print(f"✅ 配置文件已创建: {config_file}")
        else:
            print("⚠️ 默认配置文件不存在")
    else:
        print(f"✅ 配置文件已存在: {config_file}")


def setup_environment():
    """设置环境变量"""
    print("🌍 设置环境变量...")
    
    env_file = Path(".env")
    
    if not env_file.exists():
        with open(env_file, 'w') as f:
            f.write("# AI News Agent 环境变量\n")
            f.write("# OPENAI_API_KEY=your-openai-api-key\n")
            f.write("# ANTHROPIC_API_KEY=your-anthropic-api-key\n")
            f.write("# AI_NEWS_AGENT_LOG_LEVEL=INFO\n")
            f.write("# AI_NEWS_AGENT_OUTPUT_PATH=output\n")
        
        print(f"✅ 环境变量模板已创建: {env_file}")
        print("⚠️ 请编辑 .env 文件并设置您的API密钥")
    else:
        print(f"✅ 环境变量文件已存在: {env_file}")


def create_output_directory():
    """创建输出目录"""
    print("📁 创建输出目录...")
    
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    print("✅ 输出目录已创建")


def verify_installation():
    """验证安装"""
    print("✅ 验证安装...")
    
    try:
        # 测试导入
        sys.path.insert(0, "src")
        from ai_news_agent import AINewsAgent
        from ai_news_agent.config import ConfigManager
        
        print("✅ 模块导入成功")
        
        # 测试配置
        config_manager = ConfigManager()
        config = config_manager.config
        
        print(f"✅ 配置加载成功: {config.app_name} v{config.version}")
        
        # 测试CLI
        result = run_command("python -m ai_news_agent.cli --help", check=False)
        if result.returncode == 0:
            print("✅ CLI接口正常")
        else:
            print("⚠️ CLI接口可能有问题")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("🚀 AI News Agent 部署脚本")
    print("=" * 50)
    
    try:
        # 1. 检查Python版本
        check_python_version()
        
        # 2. 安装依赖
        install_dependencies()
        
        # 3. 运行测试
        run_tests()
        
        # 4. 创建配置
        create_config()
        
        # 5. 设置环境变量
        setup_environment()
        
        # 6. 创建目录
        create_output_directory()
        
        # 7. 验证安装
        if verify_installation():
            print("\n🎉 部署成功！")
            print("\n📋 下一步:")
            print("1. 编辑 .env 文件，设置API密钥")
            print("2. 运行: python examples/basic_usage.py")
            print("3. 或使用CLI: python -m ai_news_agent.cli run --help")
        else:
            print("\n❌ 部署验证失败")
            sys.exit(1)
        
    except KeyboardInterrupt:
        print("\n⚠️ 部署被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 部署失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
